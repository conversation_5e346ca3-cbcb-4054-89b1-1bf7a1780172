# Chat Application - SSE Architecture

A modular, scalable chat application for exhibition guidance with real-time audio/video processing, built with Flask, Server-Sent Events (SSE), Kafka, and Redis.

## Architecture Overview

This application has been completely rewritten from the original monolithic design into a clean, modular architecture with Server-Sent Events (SSE):

```
chat_app/
├── config.py              # Configuration management
├── services.py            # Service connections (Kafka, Redis, APIs)
├── utils.py               # Utility functions
├── audio_processor.py     # Audio processing (TTS, ASR)
├── image_processor.py     # Image processing
├── sse_manager.py         # SSE connection and event management
├── message_handler.py     # Message handling (Kafka, SSE)
├── task_manager.py        # Async task management
├── app.py                 # Flask application with SSE support
├── main.py               # Application entry point
├── test_sse.py           # Python test script
├── test_sse.html         # HTML test page
├── requirements.txt      # Python dependencies
└── README.md            # This file
```

## Key Improvements

### 1. **Modular Design**
- **Single Responsibility**: Each module handles one specific concern
- **Dependency Injection**: Services are injected rather than globally accessed
- **Testability**: Each module can be tested independently

### 2. **Robust Error Handling**
- **Retry Mechanisms**: Automatic retry for failed operations
- **Circuit Breakers**: Graceful degradation when services are unavailable
- **Comprehensive Logging**: Structured logging with different levels

### 3. **Resource Management**
- **Connection Pooling**: Efficient Redis and HTTP connection management
- **Memory Management**: Automatic cleanup of old files and tasks
- **Graceful Shutdown**: Proper cleanup on application termination

### 4. **Async Processing**
- **Task Queues**: Non-blocking upload processing
- **Worker Pools**: Configurable number of worker processes
- **Gevent Integration**: Efficient async I/O operations

### 5. **Configuration Management**
- **Environment Variables**: All settings configurable via env vars
- **Type Safety**: Structured configuration with dataclasses
- **Validation**: Configuration validation on startup

## Features

- **Real-time Communication**: Server-Sent Events (SSE) for real-time updates
- **Audio Processing**: Text-to-Speech (TTS) and Automatic Speech Recognition (ASR)
- **Image Processing**: Base64 image upload and processing
- **Location Services**: Exhibition information based on GPS coordinates
- **Message Queuing**: Kafka-based message processing
- **Caching**: Redis-based session and data caching
- **Health Monitoring**: Health check and statistics endpoints
- **Simplified Architecture**: No WebSocket complexity, pure HTTP-based communication

## Installation

1. **Install Python Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Install System Dependencies**:
   ```bash
   # Ubuntu/Debian
   sudo apt-get install ffmpeg
   
   # macOS
   brew install ffmpeg
   ```

3. **Setup External Services**:
   - Kafka broker
   - Redis server
   - TTS service
   - ASR service

## Configuration

Configure the application using environment variables:

### Core Services
```bash
# Kafka Configuration
export CONFIG_KAFKA="localhost:9092"
export KAFKA_TOPIC="poc_dev"
export KAFKA_GROUP="web_new_002"

# Redis Configuration
export REDIS_HOST="localhost"
export REDIS_PORT="6379"
export REDIS_DB="0"
export REDIS_PASS="your_password"

# External APIs
export TTS_URL="http://localhost:8080/f5tts/tts"
export ASR_URL="http://localhost:8110/asr/asr_offline"
export EXHIBITION_API_URL="http://localhost:5606/get_exhibition_LatLon"
```

### File Storage
```bash
export STATIC_TMP="static/tmp"
export BASE_SHARE_DIR="/path/to/shared/storage"
export TMP_AUDIO_RETENTION_SEC="43200"  # 12 hours
```

### Application Settings
```bash
export APP_HOST="0.0.0.0"
export APP_PORT="9214"
export FLASK_SECRET="your_secret_key"
export LOG_DIR="./logs"
```

## Usage

### Start the Application

#### Method 1: Using the enhanced run script (Recommended)
```bash
# Check dependencies first
python run.py check-deps

# Check external services
python run.py check-services

# Start the application
python run.py start

# Start with debug mode
python run.py start --debug

# Start on different port
python run.py start --port 9214
```

#### Method 2: Direct startup
```bash
python main.py
```

### Stop the Application

#### Method 1: Using the stop script
```bash
# Stop gracefully
python stop.py

# Force stop
python stop.py --force

# Stop process on specific port
python stop.py --port 9213
```

#### Method 2: Keyboard interrupt
Press `Ctrl+C` in the terminal where the application is running. If it doesn't respond immediately, wait a few seconds for graceful shutdown.

#### Method 3: Manual process termination
```bash
# Find the process
ps aux | grep python | grep main.py

# Kill by PID
kill <PID>

# Or force kill
kill -9 <PID>
```

### API Endpoints

#### Upload Endpoint
```bash
POST /upload
Content-Type: application/json

{
    "id": "device_123",
    "audio": "data:audio/wav;base64,UklGRnoGAABXQVZFZm10...",
    "video": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...",
    "longitude": 116.3974,
    "latitude": 39.9093
}
```

#### Health Check
```bash
GET /health
```

#### Statistics
```bash
GET /stats
```

### SSE Connection
```javascript
// Method 1: Upload with immediate SSE response
fetch('/upload', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({
        id: 'device_123',
        audio: 'data:audio/wav;base64,...',
        video: 'data:image/jpeg;base64,...',
        longitude: 116.3974,
        latitude: 39.9093
    })
}).then(response => {
    // Response is an SSE stream
    const reader = response.body.getReader();
    // Read SSE events...
});

// Method 2: Separate SSE stream
const eventSource = new EventSource('/stream/device_123');

eventSource.addEventListener('audio', function(event) {
    const data = JSON.parse(event.data);
    console.log('Audio ready:', data.wav);
});

eventSource.addEventListener('status', function(event) {
    const data = JSON.parse(event.data);
    console.log('Status:', data.status, data.message);
});
```

## Development

### Running Tests

#### Python Test Script
```bash
python test_sse.py
```

#### HTML Test Page
1. Start the application
2. Open `test_sse.html` in a web browser
3. Configure server URL and device ID
4. Test SSE connections and upload functionality

#### Unit Tests
```bash
pytest tests/
```

### Code Formatting
```bash
black .
flake8 .
```

### Adding New Features

1. **New Processor**: Add to appropriate processor module
2. **New Service**: Add to `services.py` with proper connection management
3. **New Route**: Add to `app.py` with proper error handling
4. **New Configuration**: Add to `config.py` with environment variable support

## Monitoring

### Health Checks
The application provides health check endpoints that monitor:
- Service connectivity (Redis, Kafka)
- Worker status
- Resource usage

### Logging
Structured logging with multiple levels:
- **DEBUG**: Detailed operation logs
- **INFO**: General application flow
- **WARNING**: Recoverable errors
- **ERROR**: Serious errors requiring attention

### Statistics
Real-time statistics available via `/stats` endpoint:
- Task processing metrics
- Message queue status
- Resource usage
- Performance metrics

## Deployment

### Docker Deployment
```dockerfile
FROM python:3.9-slim

# Install system dependencies
RUN apt-get update && apt-get install -y ffmpeg

# Install Python dependencies
COPY requirements.txt .
RUN pip install -r requirements.txt

# Copy application
COPY . /app
WORKDIR /app

# Run application
CMD ["python", "main.py"]
```

### Production Considerations
- Use a process manager like supervisord or systemd
- Configure proper log rotation
- Set up monitoring and alerting
- Use environment-specific configuration
- Implement proper security measures

## Troubleshooting

### Common Issues

1. **FFmpeg not found**: Install FFmpeg system package
2. **Kafka connection failed**: Check Kafka broker configuration
3. **Redis connection failed**: Verify Redis server and credentials
4. **WebSocket connection issues**: Check CORS and firewall settings

### Debug Mode
Enable debug logging:
```bash
export LOG_LEVEL="DEBUG"
python main.py
```

## License

This project is licensed under the MIT License.
