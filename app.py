"""
Flask web application module with SSE support.
Handles HTTP routes and Server-Sent Events for the chat application.
"""

import json
import traceback
from typing import Dict, Any
import gevent
from flask import Flask, request, Response
from flask_cors import CORS
from loguru import logger

from config import Config
from services import ServiceManager
from audio_processor import AudioProcessor
from image_processor import ImageProcessor
from message_handler import MessageHandler
from task_manager import TaskManager
from sse_manager import SSEManager
from utils import safe_json_loads, ensure_directory_exists


class ChatApplication:
    """Main chat application class that coordinates all components."""
    
    def __init__(self, config: Config):
        self.config = config
        
        # Initialize Flask app
        self.app = Flask(__name__, 
                        static_folder="static", 
                        static_url_path="/chat/static")
        self.app.config['SECRET_KEY'] = config.app.secret_key
        self.app.config.update(RESTFUL_JSON=dict(ensure_ascii=False))
        
        # Setup CORS
        self.cors = CORS()
        self.cors.init_app(app=self.app, supports_credentials=True)
        
        # Initialize services
        self.service_manager = ServiceManager(config)
        self.audio_processor = None
        self.image_processor = None
        self.sse_manager = None
        self.message_handler = None
        self.task_manager = None
        
        # Setup routes
        self._setup_routes()
        
        # Ensure required directories exist
        ensure_directory_exists(config.file.static_tmp_dir)
        ensure_directory_exists(config.file.base_share_dir)
    
    def initialize(self) -> bool:
        """Initialize all application components."""
        try:
            # Initialize services
            if not self.service_manager.initialize_all():
                logger.error("Failed to initialize services")
                return False
            
            # Initialize processors
            self.audio_processor = AudioProcessor(
                self.config,
                self.service_manager.external_api
            )
            self.image_processor = ImageProcessor(self.config)

            # Initialize SSE manager
            self.sse_manager = SSEManager(self.config)

            # Initialize message handler
            self.message_handler = MessageHandler(
                self.config,
                self.service_manager,
                self.audio_processor,
                self.sse_manager
            )

            # Initialize task manager
            self.task_manager = TaskManager(
                self.config,
                self.service_manager,
                self.audio_processor,
                self.image_processor,
                self.sse_manager
            )
            
            logger.info("Chat application initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize chat application: {e}")
            return False
    
    def start_background_tasks(self) -> None:
        """Start background tasks and workers."""
        try:
            # Start Kafka consumer
            self.message_handler.start_kafka_consumer()
            
            # Start task workers
            self.task_manager.start_workers()
            
            # Start cleanup tasks
            gevent.spawn(self._cleanup_loop)
            
            logger.info("Background tasks started")
            
        except Exception as e:
            logger.error(f"Failed to start background tasks: {e}")
    
    def shutdown(self) -> None:
        """Gracefully shutdown the application."""
        try:
            logger.info("Shutting down chat application...")
            
            # Stop background tasks
            if self.message_handler:
                self.message_handler.stop_kafka_consumer()
            
            if self.task_manager:
                self.task_manager.stop_workers()
            
            # Close service connections
            self.service_manager.close_all()
            
            logger.info("Chat application shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
    
    def _setup_routes(self) -> None:
        """Setup HTTP and SSE routes."""

        @self.app.route("/upload", methods=["POST"])
        def upload_handler():
            """Handle file upload requests."""
            return self._handle_upload()

        @self.app.route("/health", methods=["GET"])
        def health_check():
            """Health check endpoint."""
            return self._handle_health_check()

        @self.app.route("/stats", methods=["GET"])
        def stats_handler():
            """Statistics endpoint."""
            return self._handle_stats()

        @self.app.route("/stream/<device_id>", methods=["GET"])
        def sse_stream_handler(device_id):
            """Handle SSE stream connections."""
            return self._handle_sse_stream(device_id)
    
    def _handle_upload(self) -> Response:
        """
        Handle upload requests with audio, image, and location data.
        Returns an SSE stream for real-time updates.

        Returns:
            SSE Response stream
        """
        try:
            # Parse request body
            body = request.get_json(force=True, silent=True) or {}
            device_id = body.get("id")

            if not device_id:
                logger.warning("Upload request missing device ID")
                return Response(
                    json.dumps({"error": "Missing device ID"}),
                    status=400,
                    content_type='application/json'
                )

            # Validate request data
            audio_b64 = body.get("audio")
            image_b64 = body.get("video")  # Note: field name is "video" but contains image
            longitude = body.get("longitude")
            latitude = body.get("latitude")

            if not any([audio_b64, image_b64]):
                logger.warning(f"Upload request from {device_id} has no audio or image data")
                return Response(
                    json.dumps({"error": "No audio or image data provided"}),
                    status=400,
                    content_type='application/json'
                )

            # Create SSE session
            session_id = self.sse_manager.create_session(device_id)

            # Submit task for processing (async)
            upload_data = {
                "id": device_id,
                "audio": audio_b64,
                "video": image_b64,
                "longitude": longitude,
                "latitude": latitude,
                "session_id": session_id
            }

            if self.task_manager.submit_upload_task(device_id, upload_data):
                logger.info(f"Upload task submitted for device {device_id}, session {session_id}")

                # Return SSE stream
                return Response(
                    self.sse_manager.create_event_stream(session_id),
                    mimetype='text/event-stream',
                    headers={
                        'Cache-Control': 'no-cache',
                        'Connection': 'keep-alive',
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Headers': 'Cache-Control'
                    }
                )
            else:
                logger.error(f"Failed to submit upload task for device {device_id}")
                return Response(
                    json.dumps({"error": "Failed to process upload"}),
                    status=500,
                    content_type='application/json'
                )

        except Exception as e:
            logger.error(f"Upload handler error: {e}\n{traceback.format_exc()}")
            return Response(
                json.dumps({"error": "Internal server error"}),
                status=500,
                content_type='application/json'
            )
    
    def _handle_health_check(self) -> str:
        """Handle health check requests."""
        try:
            health_status = {
                "status": "healthy",
                "timestamp": gevent.time.time(),
                "services": {
                    "redis": self.service_manager.redis.client.ping() if self.service_manager.redis._client else False,
                    "kafka": self.service_manager.is_initialized,
                    "task_manager": self.task_manager.running if self.task_manager else False,
                    "message_handler": self.message_handler._running if self.message_handler else False
                }
            }
            
            # Check if any critical services are down
            if not all(health_status["services"].values()):
                health_status["status"] = "degraded"
            
            return json.dumps(health_status, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"Health check error: {e}")
            return json.dumps({
                "status": "unhealthy",
                "error": str(e)
            }, ensure_ascii=False)
    
    def _handle_stats(self) -> str:
        """Handle statistics requests."""
        try:
            stats = {
                "timestamp": gevent.time.time(),
                "task_manager": self.task_manager.get_stats() if self.task_manager else {},
                "message_handler": self.message_handler.get_message_stats() if self.message_handler else {},
                "audio_processor": self.audio_processor.get_audio_stats() if self.audio_processor else {},
                "image_processor": self.image_processor.get_image_stats() if self.image_processor else {}
            }
            
            return json.dumps(stats, ensure_ascii=False)

        except Exception as e:
            logger.error(f"Stats handler error: {e}")
            return json.dumps({
                "error": str(e)
            }, ensure_ascii=False)

    def _handle_sse_stream(self, device_id: str) -> Response:
        """
        Handle SSE stream connections for a specific device.

        Args:
            device_id: Device identifier

        Returns:
            SSE Response stream
        """
        try:
            # Create SSE session for the device
            session_id = self.sse_manager.create_session(device_id)
            logger.info(f"Created SSE stream for device {device_id}, session {session_id}")

            # Return SSE stream
            return Response(
                self.sse_manager.create_event_stream(session_id),
                mimetype='text/event-stream',
                headers={
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive',
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Headers': 'Cache-Control'
                }
            )

        except Exception as e:
            logger.error(f"SSE stream error for device {device_id}: {e}\n{traceback.format_exc()}")
            return Response(
                json.dumps({"error": "Failed to create SSE stream"}),
                status=500,
                content_type='application/json'
            )
    
    def _cleanup_loop(self) -> None:
        """Background cleanup loop for temporary files and old tasks."""
        while True:
            try:
                # Clean up old audio files
                if self.audio_processor:
                    self.audio_processor.cleanup_old_audio_files()
                
                # Clean up old image files
                if self.image_processor:
                    self.image_processor.cleanup_old_images()
                
                # Clean up old tasks
                if self.task_manager:
                    self.task_manager.cleanup_old_tasks()
                
                logger.debug("Cleanup cycle completed")
                
            except Exception as e:
                logger.error(f"Cleanup loop error: {e}")
            
            # Sleep for 10 minutes
            gevent.sleep(600)


def create_app(config: Config) -> ChatApplication:
    """
    Factory function to create and configure the chat application.
    
    Args:
        config: Application configuration
        
    Returns:
        Configured ChatApplication instance
    """
    app = ChatApplication(config)
    
    if not app.initialize():
        raise RuntimeError("Failed to initialize chat application")
    
    return app
