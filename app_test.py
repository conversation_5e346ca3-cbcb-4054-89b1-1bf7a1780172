from gevent import monkey
monkey.patch_all()
import os
import json
import requests
import time
import subprocess
import traceback
import redis
import gevent
from flask import Flask
from flask_sockets import Sockets
from flask_cors import CORS
from loguru import logger
from gevent import pywsgi
from geventwebsocket.handler import <PERSON>SocketHandler
from base64 import b64decode
from kafka import KafkaConsumer, KafkaProducer
from threading import Lock
import re
from random import randint
import hashlib
import threading
from dashscope.audio.tts_v2 import *
import dashscope
import math

app = Flask(__name__, static_folder="static", static_url_path="/chat/static")
app.config['SECRET_KEY'] = 'snmvui4390hf80984h7u3zdsf3'
cors = CORS()
cors.init_app(app=app, supports_credentials=True)
app.config.update(RESTFUL_JSON=dict(ensure_ascii=False))

CONFIG_KAFKA = ["127.0.0.1:5602"]

kafka_producer = KafkaProducer(bootstrap_servers=CONFIG_KAFKA, 
                        key_serializer=lambda k: json.dumps(k).encode(),
                        value_serializer=lambda v: json.dumps(v).encode())

redis_pool = redis.ConnectionPool(
    host='localhost',
    port=5601,
    db=0,
    password="people@123",
    max_connections=100
)

lock = Lock()

sockets = Sockets(app)

clients = {}
cost_time_list = []

# 添加日志文件（自动创建文件，无需提前配置）
logger.add("./logs/app.log") 

def get_file_name(extern_name='jpg'):
    file_name = '{}{}'.format(int(1000*time.time()), randint(1000, 9999))
    file_name = hashlib.md5(file_name.encode('utf8')).hexdigest() + '.' + extern_name
    return file_name

def consume_task():
    kafka_consumer = KafkaConsumer("poc_dev", # topic name
                                bootstrap_servers=CONFIG_KAFKA, 
                                auto_offset_reset='latest',
                                group_id='web_new_002', # group
                                enable_auto_commit=True, # 自动提交ack 
                                max_poll_interval_ms=1200000) # 超时时间
    logger.info("Start consume task success!")
    for msg in kafka_consumer:
        try:
            consume_sentence(msg) # 自定义消费函数
        except:
            logger.error(traceback.format_exc())
        gevent.sleep(0)

def consume_sentence(msg):
    data = json.loads(msg.value.decode()) # 拿到消息里的数据
    if "to" not in data or (data['to'] != "speak" and data['to'] != "stop"):
        return
    
    logger.debug(f"receive kafka message: {data}")
    id = data.get('device_id', "")
    if id not in clients:
        return
    if data['to'] == "stop":
        # try:
        #     lock.acquire()
        #     clients[id]['msg_list'].clear()
        # finally:
        #     lock.release()
        return

    sentence = data.get('text', "")
    state = data['meta_data'].get('is_end', False)
    event_id = data.get('event_id', id)
    if id in clients:
        logger.debug(f"id: {id}, sentence: {sentence}, flag: {state}")
        if clients[id]['first_sent']:
            logger.error(f"Get LLM respose time: {time.time()-clients[id]['send_text_time']}s ")
            cost_time_list.append(time.time()-clients[id]['send_text_time'])
            print("arerage time is : ", sum(cost_time_list)/len(cost_time_list))
            
        wav_file = sentence_to_wav(sentence)
        if not wav_file:
            if state == True:
                try:
                    lock.acquire()
                    clients[id]['msg_list'].append({"msg_type": "end", "meta_data": data['meta_data'], "event_id": event_id})
                finally:
                    lock.release()
            return
        
        try:
            lock.acquire()
            clients[id]['msg_list'].append({"msg_type": "wav", "wav": f"tmp/{wav_file}", "sent": sentence, "meta_data": data['meta_data'], "is_end": state, "event_id": event_id})
        finally:
            lock.release()

        if clients[id]['first_sent']:
            clients[id]['first_sent'] = False
            send_message(id)

        if state == True:
            
            try:
                lock.acquire()
                clients[id]['msg_list'].append({"msg_type": "end", "meta_data": data['meta_data'], "event_id": event_id})
            finally:
                lock.release()
    else:
        logger.warning(f"client {id} not found!")

def send_message(id):
    if id in clients:
        ws = clients[id]['ws']
        if(len(clients[id]['msg_list']) > 0):
            try:
                lock.acquire()
                next_msg = clients[id]['msg_list'][0]
                ws.send(json.dumps(next_msg))
            except Exception as e:
                logger.error(f"WebSocket send error for {id}: {e}")
            finally:
                lock.release()
            logger.warning(f"Success send wav file: {next_msg}")
            if next_msg['msg_type'] != "end":
                kafka_producer.send(topic="poc_dev", 
                                    key=time.time(), 
                                    value={"event_id": next_msg.get('event_id', id),
                                    "device_id": id,
                                    "user_id": "user0",
                                    "said_text": next_msg.get('sent', None),
                                    "event_flag": "qa",
                                    "device_type": "phone",
                                    "is_end": next_msg.get('is_end', True),
                                    "meta_data": next_msg.get('meta_data',''),
                                    "from": "speak",
                                    "to": "said",
                                    "interface": "said"})
        
            try:
                lock.acquire()
                del clients[id]['msg_list'][0]
            finally:
                lock.release()

def sentence_to_wav(sentence:str, format='aac'):
    url = "http://177.177.23.255:8080/f5tts/tts"
    data = {
        "text":sentence,
        "format":format
        }
    t1 = time.time()
    response = requests.post(url, data=data)
    if response.status_code == 200:
        content_type = response.headers.get("Content-Type")
        if content_type == f"audio/{format}":
            audio_data = response.content
            logger.info(f"tts audio data length: {len(audio_data)}" )
            if audio_data:
                file_name = get_file_name(format)
                file_path = f"static/tmp/{file_name}"  # Replace with the desired file path
                with open(file_path, "wb") as file:
                    file.write(audio_data)
                    logger.info(f"TTS Save_path: {file_path}, cost_time: {time.time()-t1}s ")
                return file_name
            else:
                logger.error("No audio data found in the result.")
        else:
            result = response.json()
            logger.error("Response:", result)
    else:
        logger.error(f"Request failed with status code: {response.status_code}, {sentence}")
    return None

def changeSampleRate(file_path, output_path):
    try:
        cmd = f"ffmpeg -i {file_path} -ac 1 -ar 16000 -sample_fmt s16 -y {output_path}"
        subprocess.run(cmd, shell=True)
    except:
        pass

def audio2Text(audio_file):
    try:
        with open(audio_file, "rb") as f:
            audio_data = f.read()
        res = requests.post("http://127.0.0.1:8110/asr/asr_offline", data=audio_data, timeout=(2.01, 5.01))
        res = res.json()
        if res['success']:
            ret = ""
            results = res['results']
            for data in results:
                ret += data['text']
            return ret
        return None
    except:
        logger.error(traceback.format_exc())
        return None

def query(id, audio_data:bytes, image_path=None, exhibition_id=0,exhibition_name=""):
    file_path = f"/private/share/chat/{id}.wav"
    output_path = f"/private/share/chat/{id}_16000.wav"
    text = None
    try:
        logger.debug(f"Receive Audio data length: {len(audio_data)}")
        if len(audio_data) > 4096:
            with open(file_path, 'wb') as f:
                f.write(audio_data)
            t1 = time.time()
            changeSampleRate(file_path, output_path)
            logger.warning(f"changeSampleRate cost time: {time.time() - t1} s, output_path: {output_path}. ")
            t1 = time.time()
            if os.path.exists(output_path):
                text = audio2Text(output_path)
                os.remove(output_path)
                logger.warning(f"audio2Text cost time: {time.time() - t1} s")
                logger.warning(f"audio2Text cost from get_audio_time: {time.time() - clients[id]['get_audio_time']}")

    except Exception as e:
        logger.error(f"audio2text error: {str(e)}")
        pass


    try:
        lock.acquire()
        clients[id]['msg_list'].clear()
    finally:
        lock.release()

    r = redis.Redis(connection_pool=redis_pool)
    
    # if text:
    clients[id]['first_sent'] = True
    clients[id]['send_text_time'] = time.time()
    
    try:
        lock.acquire()
        clients[id]['msg_list'].clear()
    finally:
        lock.release()
    if image_path:
        logger.info(f"{id}: {text}  + 图像: {image_path}")
        
        write_total = 0
        r.hset(id, "image_caption", "")
        while r.hget(id, "image_caption").decode() != "":
            r.hset(id, "image_caption", "")
            write_total = write_total + 1
            if write_total > 3:
                break
            gevent.sleep(0.01)
        kafka_producer.send(topic="poc_dev",
                                key=time.time(),
                                value={"frame": image_path,
                                    "device_id": id,
                                    "from": "client",
                                    "to": "image_caption",
                                    "device_type": "phone",
                                    "interface": "image_caption"})
        #图像rag
        # r.hset(id, "frame_rag", "None")
        r.hdel(id, "frame_rag")
        kafka_producer.send(topic="poc_dev",
                    key=time.time(),
                    value={"frame": image_path,
                            "frame_time": round(time.time(), 2),
                            "longitude": 0,
                            "latitude": 0,
                            "degree": 0,
                            "ts": 0,
                            "query": text,
                            "device_id": id,
                            "exhibition_id": exhibition_id,
                            "user_id": "user0",
                            "event_id": id,
                            "context": "",
                            "device_type": "phone",
                            "from": "client",
                            "to": "trigger",
                            "interface": "trigger"})
        logger.info(f"Send image to rag finished: {image_path}")
        
        # 发送text rag
        if text:
            # r.hset(id, "text_rag", "None")
            r.hdel(id, "text_rag")
            kafka_producer.send(topic="poc_dev",
                                        key=time.time(),
                                        value={"frame": image_path,
                                            "frame_time": round(time.time(), 2),
                                            "longitude": 0,
                                            "latitude": 0,
                                            "degree": 0,
                                            "ts": 0,
                                            "query": text,
                                            "device_id": id,
                                            "exhibition_id": exhibition_id,
                                            "user_id": "user0",
                                            "event_id": id,
                                            "context": "",
                                            "device_type": "phone",
                                            "from": "client",
                                            "to": "text_rag",
                                            "interface": "text_rag"})
            logger.info(f"Have text, Send text to rag finished: {text}")

    else: #语音模式，没有图像
        logger.info(f"Only Speech mode: {id}: {text}")

        # r.hset(id, "image_caption", "None")
        # r.hset(id, "text_rag", "None")
        # r.hset(id, "frame_rag", "None")
        r.hdel(id, "text_rag")
        r.hdel(id, "frame_rag")
        r.hdel(id, "image_caption")
        
        #给大模型发送语音
        kafka_producer.send(topic="poc_dev",
                    key=time.time(),
                    value={"frame": image_path,
                            "frame_time": round(time.time(), 2),
                            "longitude": 0,
                            "latitude": 0,
                            "degree": 0,
                            "ts": 0,
                            "query": text,
                            "device_id": id,
                            "exhibition_id": exhibition_id,
                            "user_id": "user0",
                            "event_id": id,
                            "context": "",
                            "device_type": "phone",
                            "from": "client",
                            "to": "trigger",
                            "interface": "trigger"})
        logger.info(f"Send image to model finished: {image_path}")
        
        # 文本      rag
        kafka_producer.send(topic="poc_dev",
                                    key=time.time(),
                                    value={"frame": None,
                                        "frame_time": round(time.time(), 2),
                                        "longitude": 0,
                                        "latitude": 0,
                                        "degree": 0,
                                        "ts": 0,
                                        "query": text,
                                        "device_id": id,
                                        "exhibition_id": exhibition_id,
                                        "user_id": "user0",
                                        "event_id": id,
                                        "context": "",
                                        "device_type": "phone",
                                        "from": "client",
                                        "to": "text_rag", #text_rag
                                        "interface": "text_rag"})
        logger.info(f"Send text to rag finished: {text}")
        

def get_exhibition_info(longitude, latitude):
    """
        get exhibition info from gps location
    """
    url = "http://127.0.0.1:5606/get_exhibition_LatLon"
    
    headers = {'content-type': 'application/json;charset=utf-8'}
    data ={'from':"client" ,"to":"rag", "user id":"test user" ,"device id":"test device", "Timestamp":""}
    if longitude is None or latitude is None:
        return None, None
    try:
        resp=requests.post(url ,json=data ,headers=headers)
        result = json.loads(resp.text)
        dist_list=[]
        id_name = {}
        target_name = "人民网综合展厅"
        target_id = None
        for exhibition in result['exhibition_list']:
            exhibition_id = exhibition['exhibition_id']
            exhibition_name = exhibition['name']
            if exhibition_name == target_name:
                target_id = exhibition_id
            exhibition_longitude = exhibition['longitude']
            exhibition_latitude = exhibition['latitude']
            id_name[exhibition_id] = exhibition_name
            # 计算欧式距离
            distance = math.sqrt((exhibition_longitude - longitude) ** 2 + (exhibition_latitude - latitude) ** 2)
            dist_list.append({exhibition_id:distance})
        #取距离最近的一个展览
        min_dist_exhibition = min(dist_list, key=lambda x: list(x.values())[0])
        min_dist_exhibition_id = list(min_dist_exhibition.keys())[0]
        name = id_name[min_dist_exhibition_id]
        if "人民网" in name:
            return target_id, target_name
        return min_dist_exhibition_id, name 
        
    except Exception as e:
        logger.error(f"API get_exhibition_info error: {e}")
        return None, None


@sockets.route('/chat')
def gen_socket(ws):
    r = redis.Redis(connection_pool=redis_pool)
    while not ws.closed:
        try:
            message = ws.receive()
            if not message:
                continue
            msg = json.loads(message)
            id = msg.get('id', None)
            longitude = msg.get('longitude', None)
            latitude = msg.get('latitude', None)
            exhibition_id, exhibition_name = get_exhibition_info(longitude, latitude)
            logger.warning(f"[Next==>] Received Msg, id is: {id}, longitude is: {longitude}, latitude is: {latitude}, msg_type: {msg['msg']} ")
            if id is None:
                continue

            if id not in clients:
                logger.info(f"{id}: New connected")
                clients[id] = {"ws": ws, "msg_list": [], "msg_type": msg['msg'], "first_sent": True, "get_audio_time": time.time()}
                if exhibition_id is not None:
                    logger.info(f"Firstly Get exhibition info: {exhibition_id}, {exhibition_name}")
                    r.hset(id, mapping={
                        "user_profile": json.dumps({"id": "user0", "age": "adult"}),
                        "exhibition": json.dumps({"id": exhibition_id, "name": exhibition_name, "end_id": 12, "caption": ""}),
                        "mode": "free",
                        "direction_id": 0
                    })
                else:
                    logger.error(f"Get exhibition info is None, Initialize failed.")
                    r.hset(id, mapping={
                        "user_profile": json.dumps({"id": "user0", "age": "adult"}),
                        "exhibition": json.dumps({"id": 0, "name": "19层", "end_id": 12, "caption": ""}),
                        "mode": "free",
                        "direction_id": 0
                    })

                # kafka_producer.send(
                #     topic="poc_dev",
                #     key=time.time(),
                #     value={
                #         "user_id": "user0",
                #         "device_id": id,
                #         "mode": "free",
                #         "exhibition_id": exhibition_id,
                #         "from": "client",
                #         "to": "create_session",
                #         "device_type": "phone",
                #         "interface": "create_session",
                #         "user_profile": {
                #             "id": "user0",
                #             "age": "adult"
                #         }
                #     }
                # )
                
                
            if ws != clients[id]['ws']:
                clients[id]['ws'] = ws
                
            msg_type = msg['msg']
            if msg_type == "query":
                audio_data = msg['audio']
                audio_data = audio_data[audio_data.find(',')+1:]
                audio_data = b64decode(audio_data)

                if msg.get('video', None):
                    image_data = msg['video']
                    image_data = image_data[image_data.find(',')+1:]
                    image_data = b64decode(image_data)
                    file_name = get_file_name("jpg")
                    image_path = f"/private/share/chat/{file_name}"
                    with open(image_path, 'wb') as f:
                        f.write(image_data)
                    query(id, audio_data, image_path, exhibition_id=exhibition_id, exhibition_name=exhibition_name)
                else:
                    query(id, audio_data, image_path=None, exhibition_id=exhibition_id, exhibition_name=exhibition_name)

            elif msg_type == "play_end":
                send_message(id)
        except Exception as e:
            logger.error(f"WebSocket receive error: {e}")
            break
    
    for k in clients.keys():
        if clients[k]['ws'] == ws:
            logger.warning(f"id: {k}: closed")
            del clients[k]
            break


if __name__ == "__main__":
    gevent.spawn(consume_task)
    
    logger.debug(app.url_map)

    server = pywsgi.WSGIServer(("0.0.0.0", 9214), app, handler_class=WebSocketHandler)
    server.serve_forever()
