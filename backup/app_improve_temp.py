# app_test_improved.py
from gevent import monkey
monkey.patch_all()

import os
import json
import requests
import time
import subprocess
import traceback
import redis
import gevent
from flask import Flask
from flask_sockets import Sockets
from flask_cors import CORS
from loguru import logger
from gevent import pywsgi
from geventwebsocket.handler import WebSocketHandler
from base64 import b64decode
from kafka import KafkaConsumer, KafkaProducer
from threading import RLock
from collections import deque
import hashlib
import math
from random import randint
from flask import Flask, request
import queue

# --------------- Configuration (prefer env vars) ----------------
CONFIG_KAFKA = os.getenv("CONFIG_KAFKA", "127.0.0.1:5602").split(",")
KAFKA_TOPIC = os.getenv("KAFKA_TOPIC", "poc_dev")
REDIS_HOST = os.getenv("REDIS_HOST", "localhost")
REDIS_PORT = int(os.getenv("REDIS_PORT", "5601"))
REDIS_DB = int(os.getenv("REDIS_DB", "0"))
REDIS_PASS = os.getenv("REDIS_PASS", "people@123")
TTS_URL = os.getenv("TTS_URL", "http://**************:8080/f5tts/tts")
ASR_URL = os.getenv("ASR_URL", "http://127.0.0.1:8110/asr/asr_offline")
STATIC_TMP = os.getenv("STATIC_TMP", "static/tmp")
TMP_AUDIO_DIR = STATIC_TMP  # same
BASE_SHARE_DIR = os.getenv("BASE_SHARE_DIR", "/private/share/chat")
TMP_AUDIO_RETENTION_SEC = int(os.getenv("TMP_AUDIO_RETENTION_SEC", str(60*60*12)))  # 12 hours
TTS_TIMEOUT = (2, 10)   # connect, read
ASR_TIMEOUT = (2.0, 5.0)
FFMPEG_TIMEOUT = int(os.getenv("FFMPEG_TIMEOUT", "15"))
KAFKA_CONSUMER_GROUP = os.getenv("KAFKA_GROUP", "web_new_002")

# --------------- App init ----------------
app = Flask(__name__, static_folder="static", static_url_path="/chat/static")
app.config['SECRET_KEY'] = os.getenv("FLASK_SECRET", "replace_this_secret")
cors = CORS()
cors.init_app(app=app, supports_credentials=True)
app.config.update(RESTFUL_JSON=dict(ensure_ascii=False))

# logging
os.makedirs("./logs", exist_ok=True)
logger.add("./logs/app.log", rotation="10 MB", retention="10 days")

# ensure tmp dir exists
os.makedirs(TMP_AUDIO_DIR, exist_ok=True)

# Kafka producer (robustified)
def create_kafka_producer():
    return KafkaProducer(
        bootstrap_servers=CONFIG_KAFKA,
        key_serializer=lambda k: json.dumps(k).encode(),
        value_serializer=lambda v: json.dumps(v).encode(),
        acks=1,
        linger_ms=5,
        max_request_size=10 * 1024 * 1024
    )

kafka_producer = create_kafka_producer()

# Redis pool
redis_pool = redis.ConnectionPool(
    host=REDIS_HOST,
    port=REDIS_PORT,
    db=REDIS_DB,
    password=REDIS_PASS,
    max_connections=100
)
r = redis.Redis(connection_pool=redis_pool)

# global structures and locks
clients = {}  # id -> {ws, msg_list (deque), first_sent, ...}
clients_lock = RLock()
MAX_MSG_QUEUE = 200  # per client max queued TTS messages
cost_time_list = deque(maxlen=1000)

sockets = Sockets(app)

# helpers
def get_file_name(extern_name='jpg'):
    file_name = f"{int(time.time()*1000)}{randint(1000,9999)}"
    return hashlib.md5(file_name.encode('utf8')).hexdigest() + '.' + extern_name

def haversine(lon1, lat1, lon2, lat2):
    # return distance in meters approximately
    R = 6371000.0
    phi1 = math.radians(lat1); phi2 = math.radians(lat2)
    dphi = math.radians(lat2 - lat1)
    dlambda = math.radians(lon2 - lon1)
    a = math.sin(dphi/2)**2 + math.cos(phi1)*math.cos(phi2)*math.sin(dlambda/2)**2
    c = 2*math.atan2(math.sqrt(a), math.sqrt(1-a))
    return R * c

# ----------------- Background tasks -----------------
def consume_task():
    kafka_consumer = KafkaConsumer(
        KAFKA_TOPIC,
        bootstrap_servers=CONFIG_KAFKA,
        auto_offset_reset='latest',
        group_id=KAFKA_CONSUMER_GROUP,
        enable_auto_commit=True,
        max_poll_interval_ms=1200000
    )
    logger.info("Start consume task success!")
    for msg in kafka_consumer:
        try:
            consume_sentence(msg)
        except Exception:
            logger.error(traceback.format_exc())
        gevent.sleep(0)

def cleanup_tmp_files_loop():
    """Periodically remove old temp audio files."""
    while True:
        try:
            now = time.time()
            for fname in os.listdir(TMP_AUDIO_DIR):
                fpath = os.path.join(TMP_AUDIO_DIR, fname)
                if not os.path.isfile(fpath):
                    continue
                try:
                    mtime = os.path.getmtime(fpath)
                except OSError:
                    continue
                if now - mtime > TMP_AUDIO_RETENTION_SEC:
                    try:
                        os.remove(fpath)
                        logger.info(f"Removed old tmp file: {fpath}")
                    except Exception:
                        logger.warning(f"Failed to remove tmp file: {fpath} - {traceback.format_exc()}")
        except Exception:
            logger.error(traceback.format_exc())
        gevent.sleep(60 * 10)  # every 10 minutes

gevent.spawn(cleanup_tmp_files_loop)

# --------------- Core functions -----------------
def safe_kafka_send(value, key=None):
    try:
        kafka_producer.send(topic=KAFKA_TOPIC, key=key if key is not None else time.time(), value=value)
    except Exception:
        logger.error(f"Kafka send error: {traceback.format_exc()}")

def sentence_to_wav(sentence: str, fmt='aac'):
    if not sentence:
        return None
    data = {"text": sentence, "format": fmt}
    t1 = time.time()
    try:
        resp = requests.post(TTS_URL, data=data, timeout=TTS_TIMEOUT)
    except Exception as e:
        logger.error(f"TTS request failed: {e}")
        return None

    if resp.status_code == 200:
        content_type = resp.headers.get("Content-Type", "")
        if content_type.startswith(f"audio/") or isinstance(resp.content, (bytes, bytearray)):
            audio_data = resp.content
            if not audio_data:
                logger.error("TTS returned empty audio")
                return None
            file_name = get_file_name(fmt)
            file_path = os.path.join(TMP_AUDIO_DIR, file_name)
            try:
                with open(file_path, "wb") as f:
                    f.write(audio_data)
                logger.info(f"TTS saved {file_path}, cost={time.time()-t1:.2f}s")
                return file_name
            except Exception as e:
                logger.error(f"Write TTS file failed: {e}")
                return None
        else:
            # try to parse json error
            try:
                logger.error(f"TTS unexpected content-type {content_type}, body={resp.text}")
            except Exception:
                logger.error("TTS unexpected response")
    else:
        logger.error(f"TTS HTTP {resp.status_code}: {resp.text[:200]}")
    return None

def changeSampleRate(file_path, output_path):
    cmd = ["ffmpeg", "-y", "-i", file_path, "-ac", "1", "-ar", "16000", "-sample_fmt", "s16", output_path]
    try:
        subprocess.run(cmd, timeout=FFMPEG_TIMEOUT, check=True)
        return True
    except subprocess.TimeoutExpired:
        logger.error("ffmpeg timeout")
    except subprocess.CalledProcessError as e:
        logger.error(f"ffmpeg failed: {e}")
    except Exception:
        logger.error(traceback.format_exc())
    return False

def audio2Text(audio_file):
    try:
        with open(audio_file, "rb") as f:
            audio_data = f.read()
        res = requests.post(ASR_URL, data=audio_data, timeout=ASR_TIMEOUT)
        res_json = res.json()
        if res_json.get('success'):
            ret = "".join([d.get('text', '') for d in res_json.get('results', [])])
            return ret
        logger.warning(f"ASR returned success=false: {res_json}")
    except Exception:
        logger.error(traceback.format_exc())
    return None

def safe_append_msg(client, item):
    with clients_lock:
        q = client['msg_list']
        if len(q) >= MAX_MSG_QUEUE:
            # drop oldest to avoid memory explosion
            q.popleft()
        q.append(item)

def consume_sentence(msg):
    try:
        data = json.loads(msg.value.decode())
    except Exception:
        logger.error("Invalid kafka message")
        return
    if "to" not in data or data['to'] not in ("speak", "stop"):
        return
    logger.debug(f"receive kafka message: {data}")
    id = data.get('device_id', "")
    with clients_lock:
        client = clients.get(id)
    if not client:
        return

    if data['to'] == "stop":
        return

    sentence = data.get('text', "")
    state = data.get('meta_data', {}).get('is_end', False)
    event_id = data.get('event_id', id)

    if client.get('first_sent'):
        client['first_sent'] = False
        # maintain latency metric
        # logger.error(f"Get LLM respose time: {time.time()-clients[id]['send_text_time']}s ")

    wav_file = sentence_to_wav(sentence)
    if not wav_file:
        if state: # end of conversation
            safe_append_msg(client, {"msg_type": "end", "meta_data": data.get('meta_data', {}), "event_id": event_id})
        return
    # queue audio message
    safe_append_msg(client, {"msg_type": "wav", "wav": f"tmp/{wav_file}", "sent": sentence, "meta_data": data.get('meta_data', {}), "is_end": state, "event_id": event_id})

    # trigger immediate send if waiting
    if client.get('waiting_for_play', False) is not True:
        # send immediate if the client expects first_sent
        gevent.spawn(send_message_safe, id)
        client['waiting_for_play'] = True

    if state:
        safe_append_msg(client, {"msg_type": "end", "meta_data": data.get('meta_data', {}), "event_id": event_id})

def send_message_safe(id, max_retries=3): 
    with clients_lock:
        client = clients.get(id)
        if not client:
            return
        ws = client.get('ws')
        if not ws:
            return
        if len(client['msg_list']) == 0:
            return
        next_msg = client['msg_list'][0]

    # attempt send with retries, but don't block forever
    attempt = 0
    sent = False
    while attempt < max_retries:
        attempt += 1
        try:
            ws.send(json.dumps(next_msg))
            sent = True
            logger.warning(f" attempt: {attempt}, Success send wav file: {next_msg},")
            break
        except Exception as e:
            logger.error(f"WebSocket send error for {id}, attempt {attempt}: {e}")
            gevent.sleep(0.2 * attempt)
    if not sent:
        logger.error(f"Failed to send message to {id} after {max_retries} attempts, will keep in queue for next try")
        return

    # on success - produce kafka said event (non-blocking)
    try:
        if next_msg.get('msg_type') != "end":
            safe_kafka_send({
                "event_id": next_msg.get('event_id', id),
                "device_id": id,
                "user_id": "user0",
                "said_text": next_msg.get('sent', None),
                "event_flag": "qa",
                "device_type": "phone",
                "is_end": next_msg.get('is_end', True),
                "meta_data": next_msg.get('meta_data',''),
                "from": "speak",
                "to": "said",
                "interface": "said"
            }, key=time.time())
    except Exception:
        logger.error(traceback.format_exc())

    # remove from queue after success
    with clients_lock:
        client = clients.get(id)
        if client and len(client['msg_list']) > 0:
            # ensure same message still at head (race-safe isn't perfect but good)
            try:
                client['msg_list'].popleft()
            except Exception:
                logger.error("Failed to popleft after send")

def get_exhibition_info(longitude, latitude):
    if longitude is None or latitude is None:
        return None, None
    url = "http://127.0.0.1:5606/get_exhibition_LatLon"
    headers = {'content-type': 'application/json;charset=utf-8'}
    data = {'from': "client", "to": "rag", "user id": "test user", "device id": "test device", "Timestamp": ""}
    try:
        resp = requests.post(url, json=data, headers=headers, timeout=(2, 5))
        result = resp.json()
        best = None
        best_dist = None
        target_id = None
        target_name = "人民网综合展厅"
        for exhibition in result.get('exhibition_list', []):
            eid = exhibition.get('exhibition_id')
            name = exhibition.get('name')
            lon = exhibition.get('longitude')
            lat = exhibition.get('latitude')
            if name == target_name:
                target_id = eid
            if lon is None or lat is None:
                continue
            dist = haversine(longitude, latitude, lon, lat)
            if best is None or dist < best_dist:
                best = (eid, name)
                best_dist = dist
        if best:
            eid, name = best
            if "人民网" in name and target_id:
                return target_id, target_name
            return eid, name
    except Exception:
        logger.error(f"API get_exhibition_info error: {traceback.format_exc()}")
    return None, None

# 存储任务
tasks = {}
task_queue = queue.Queue()

@app.route("/upload", methods=["POST"])
def chat_stream():
    """
    post upload image and audio
    """
    body = request.get_json(force=True, silent=True) or {}
    id = body.get("id", None)
    audio_b64 = body.get("audio",None)
    image_b64 = body.get("video",None)
    longitude = body.get("longitude",None)
    latitude = body.get("latitude",None)
    
    header = {
            "msg_type": "header",
            "id": id,
            "status": "thinking"
        }
    user_data = {"id": id, "audio": audio_b64, "image": image_b64, "longitude": longitude, "latitude": latitude}
    # 把任务放到队列中
    task_id = id
    tasks[task_id] = user_data
    task_queue.put(task_id)
    
    # 立即返回，后台 worker 会处理
    gevent.spawn(worker)
    
    return json.dumps(header, ensure_ascii=False)

def worker():
    """
        initiate a websocket connection and send messages
    """
    while True:
        task_id = task_queue.get()
        user_data = tasks.get(task_id, {})
        if not user_data:
            continue
        id = user_data.get("id", None)
        audio_b64 = user_data.get("audio")
        image_b64 = user_data.get("image")
        longitude = user_data.get("longitude")
        latitude = user_data.get("latitude")
        
        exhibition_id, exhibition_name = get_exhibition_info(longitude, latitude)
        
        if exhibition_id is not None:
            logger.info(f"Firstly Get exhibition info: {exhibition_id}, {exhibition_name}")
            r.hset(id, mapping={
                "user_profile": json.dumps({"id": "user0", "age": "adult"}),
                "exhibition": json.dumps({"id": exhibition_id, "name": exhibition_name, "end_id": 12, "caption": ""}),
                "mode": "free",
                "direction_id": 0
            })
        else:
            r.hset(id, mapping={
                "user_profile": json.dumps({"id": "user0", "age": "adult"}),
                "exhibition": json.dumps({"id": 0, "name": "19层", "end_id": 12, "caption": ""}),
                "mode": "free",
                "direction_id": 0
            })
            
        # Preprocess inputs (ASR + image save) synchronously before starting the stream
        image_path = None
        asr_text = None
        try:
            # save audio
            if audio_b64:
                audio_data = audio_b64[audio_b64.find(',')+1:]
                audio_data = b64decode(audio_data)
                raw_path = os.path.join(BASE_SHARE_DIR, f"{id}.wav")
                with open(raw_path, "wb") as f:
                    f.write(audio_data)
                # resample and ASR
                out_path = os.path.join(BASE_SHARE_DIR, f"{id}_16000.wav")
                if changeSampleRate(raw_path, out_path) and os.path.exists(out_path):
                    t0 = time.time()
                    asr_text = audio2Text(out_path)
                    logger.info(f"ASR done in {time.time()-t0:.2f}s, text='{asr_text}'")
                    try:
                        os.remove(out_path)
                    except Exception:
                        pass

            # save image if any
            if image_b64:
                image_data = image_b64
                image_data = image_data[image_data.find(',')+1:]
                img_data = b64decode(image_data)
                
                img_name = get_file_name("jpg")
                image_path = os.path.join(BASE_SHARE_DIR, img_name)
                with open(image_path, "wb") as f:
                    f.write(img_data)
        except Exception:
            logger.error("Preprocess failed:\n" + traceback.format_exc())


        logger.info(f" {id}: {asr_text}  + 图像: {image_path}")
        # frame_rag AND to model server
        safe_kafka_send({
            "frame": image_path,
            "frame_time": round(time.time(), 2),
            "longitude": 0,
            "latitude": 0,
            "degree": 0, "ts": 0,
            "query": asr_text,
            "device_id": id,
            "exhibition_id": exhibition_id,
            "user_id": "user0",
            "event_id": id,
            "context": "",
            "device_type": "phone",
            "from": "client", "to": "trigger", "interface": "trigger"})
        logger.info(f"Send Image_path to Kafka finished: {image_path}")

        if asr_text:
            r.hdel(id, "text_rag")
            safe_kafka_send({"frame": None,
                    "frame_time": round(time.time(), 2),
                    "longitude": 0,
                    "latitude": 0,
                    "degree": 0,
                    "ts": 0,
                    "query": asr_text,
                    "device_id": id,
                    "exhibition_id": exhibition_id,
                    "user_id": "user0",
                    "event_id": id,
                    "context": "",
                    "device_type": "phone",
                    "from": "client",
                    "to": "text_rag", #text_rag
                    "interface": "text_rag"})
            logger.info(f"Send text to rag finished: {asr_text}")

        if image_path:
            r.hdel(id, "frame_rag")
            r.hdel(id, "image_caption")
            # image_caption
            safe_kafka_send({"frame": image_path,
                            "device_id": id, 
                            "from": "client", 
                            "to": "image_caption",
                            "device_type": "phone",
                            "interface": "image_caption"})
            logger.info(f"Send image to image_caption finished: {image_path}")
    
# --------------- WebSocket route -----------------
@sockets.route('/chat')
def gen_socket(ws):
    while not ws.closed:
        try:
            message = ws.receive()
            # logger.info(f"[<==Next] Received Msg, id is: {id}")
            if not message:
                gevent.sleep(0.01)
                continue
            msg = json.loads(message)
            id = msg.get('id', None)

            logger.warning(f"[Next==>] Received Msg, id is: {id}, msg_type:{msg.get('msg')}")
            if id is None:
                continue

            with clients_lock:
                if id not in clients:
                    logger.info(f"{id}: New connected")
                    clients[id] = {
                        "ws": ws,
                        "msg_list": deque(),
                        "msg_type": msg.get('msg'),
                        "first_sent": True,
                        "get_audio_time": time.time(),
                        "waiting_for_play": False
                    }
                else:
                    # update ws handle (reconnect)
                    clients[id]['ws'] = ws

            msg_type = msg.get('msg')
            if msg_type == "query":
                logger.info(f"{id}: Query received, Do nothing! ")
                pass

            elif msg_type == "play_end":
                # client indicates finished playing current chunk -> send next
                gevent.spawn(send_message_safe, id)
            else:
                logger.debug("Unknown msg_type: %s", msg_type)

        except Exception:
            logger.error(traceback.format_exc())
            break

    # WS closed - clean client entry safely
    with clients_lock:
        remove_key = None
        for k, v in list(clients.items()):
            if v.get('ws') == ws:
                remove_key = k
                break
        if remove_key:
            logger.warning(f"id: {remove_key} closed - removing client")
            try:
                del clients[remove_key]
            except KeyError:
                pass

# --------------- Entrypoint -----------------
if __name__ == "__main__":
    # start kafka consumer in background
    gevent.spawn(consume_task)
    logger.debug(app.url_map)
    server = pywsgi.WSGIServer(("0.0.0.0", 9214), app, handler_class=WebSocketHandler)
    server.serve_forever()
