"""
Audio processing module for TTS, ASR, and audio format conversion.
Handles all audio-related operations with proper error handling and optimization.
"""

import os
import subprocess
import time
from typing import Optional, <PERSON>ple
from base64 import b64decode
from loguru import logger

from config import Config
from services import ExternalAPIService
from utils import get_file_name, safe_remove_file, PerformanceTimer, retry_on_exception


class AudioFormatConverter:
    """Handles audio format conversion using FFmpeg."""
    
    def __init__(self, config: Config):
        self.config = config
        self.ffmpeg_timeout = config.file.ffmpeg_timeout
    
    @retry_on_exception(max_retries=2, delay=0.5)
    def convert_to_16khz_mono(self, input_path: str, output_path: str) -> bool:
        """
        Convert audio file to 16kHz mono format using FFmpeg.
        
        Args:
            input_path: Path to input audio file
            output_path: Path to output audio file
            
        Returns:
            True if conversion successful, False otherwise
        """
        if not os.path.exists(input_path):
            logger.error(f"Input audio file not found: {input_path}")
            return False
        
        cmd = [
            "ffmpeg", "-y",  # overwrite output file
            "-i", input_path,
            "-ac", "1",      # mono
            "-ar", "16000",  # 16kHz sample rate
            "-sample_fmt", "s16",  # 16-bit samples
            output_path
        ]
        
        with PerformanceTimer(f"FFmpeg conversion: {os.path.basename(input_path)}"):
            try:
                result = subprocess.run(
                    cmd,
                    timeout=self.ffmpeg_timeout,
                    check=True,
                    capture_output=True,
                    text=True
                )
                
                if os.path.exists(output_path):
                    logger.debug(f"Audio conversion successful: {output_path}")
                    return True
                else:
                    logger.error("FFmpeg completed but output file not found")
                    return False
                    
            except subprocess.TimeoutExpired:
                logger.error(f"FFmpeg timeout after {self.ffmpeg_timeout}s")
                return False
            except subprocess.CalledProcessError as e:
                logger.error(f"FFmpeg failed with return code {e.returncode}: {e.stderr}")
                return False
            except Exception as e:
                logger.error(f"FFmpeg conversion error: {e}")
                return False
    
    def is_ffmpeg_available(self) -> bool:
        """Check if FFmpeg is available in the system."""
        try:
            subprocess.run(
                ["ffmpeg", "-version"],
                capture_output=True,
                check=True,
                timeout=5
            )
            return True
        except Exception:
            return False


class AudioProcessor:
    """Main audio processing class that coordinates TTS, ASR, and format conversion."""
    
    def __init__(self, config: Config, external_api_service: ExternalAPIService):
        self.config = config
        self.external_api = external_api_service
        self.converter = AudioFormatConverter(config)
        self.tmp_audio_dir = config.file.static_tmp_dir
        self.base_share_dir = config.file.base_share_dir
        
        # Ensure directories exist
        from utils import ensure_directory_exists
        ensure_directory_exists(self.tmp_audio_dir)
        ensure_directory_exists(self.base_share_dir)
    
    def text_to_audio_file(self, text: str, audio_format: str = 'aac') -> Optional[str]:
        """
        Convert text to audio file using TTS service.
        
        Args:
            text: Text to convert to speech
            audio_format: Output audio format
            
        Returns:
            Filename of generated audio file (relative to tmp_audio_dir), or None on error
        """
        if not text.strip():
            logger.warning("Empty text provided for TTS")
            return None
        
        # Get audio data from TTS service
        audio_data = self.external_api.text_to_speech(text, audio_format)
        if not audio_data:
            logger.error("TTS service returned no audio data")
            return None
        
        # Generate unique filename and save
        filename = get_file_name(audio_format)
        file_path = os.path.join(self.tmp_audio_dir, filename)
        
        try:
            with open(file_path, "wb") as f:
                f.write(audio_data)
            
            logger.info(f"TTS audio saved: {filename} ({len(audio_data)} bytes)")
            return filename
            
        except Exception as e:
            logger.error(f"Failed to save TTS audio file {file_path}: {e}")
            return None
    
    def process_base64_audio(self, audio_b64: str, device_id: str) -> Optional[str]:
        """
        Process base64 encoded audio data and convert to text using ASR.
        
        Args:
            audio_b64: Base64 encoded audio data (with data URL prefix)
            device_id: Device identifier for filename generation
            
        Returns:
            Recognized text, or None on error
        """
        if not audio_b64:
            return None
        
        try:
            # Remove data URL prefix if present
            if ',' in audio_b64:
                audio_data = audio_b64[audio_b64.find(',') + 1:]
            else:
                audio_data = audio_b64
            
            # Decode base64 audio data
            try:
                decoded_audio = b64decode(audio_data)
            except Exception as e:
                logger.error(f"Failed to decode base64 audio: {e}")
                return None
            
            # Save raw audio file
            raw_filename = f"{device_id}.wav"
            raw_path = os.path.join(self.base_share_dir, raw_filename)
            
            with open(raw_path, "wb") as f:
                f.write(decoded_audio)
            
            # Convert to 16kHz mono for ASR
            converted_filename = f"{device_id}_16000.wav"
            converted_path = os.path.join(self.base_share_dir, converted_filename)
            
            if not self.converter.convert_to_16khz_mono(raw_path, converted_path):
                logger.error("Audio format conversion failed")
                safe_remove_file(raw_path)
                return None
            
            # Perform ASR
            asr_text = self._perform_asr(converted_path)
            
            # Cleanup temporary files
            safe_remove_file(raw_path)
            safe_remove_file(converted_path)
            
            if asr_text:
                logger.info(f"ASR successful: '{asr_text}' (from {len(decoded_audio)} bytes)")
            else:
                logger.warning("ASR returned no text")
            
            return asr_text
            
        except Exception as e:
            logger.error(f"Audio processing failed: {e}")
            return None
    
    def _perform_asr(self, audio_file_path: str) -> Optional[str]:
        """
        Perform automatic speech recognition on audio file.
        
        Args:
            audio_file_path: Path to audio file
            
        Returns:
            Recognized text, or None on error
        """
        if not os.path.exists(audio_file_path):
            logger.error(f"Audio file not found for ASR: {audio_file_path}")
            return None
        
        try:
            with open(audio_file_path, "rb") as f:
                audio_data = f.read()
            
            if not audio_data:
                logger.error("Audio file is empty")
                return None
            
            return self.external_api.speech_to_text(audio_data)
            
        except Exception as e:
            logger.error(f"ASR processing failed: {e}")
            return None
    
    def cleanup_old_audio_files(self, max_age_seconds: Optional[int] = None) -> int:
        """
        Clean up old audio files from temporary directory.
        
        Args:
            max_age_seconds: Maximum age in seconds (uses config default if None)
            
        Returns:
            Number of files removed
        """
        if max_age_seconds is None:
            max_age_seconds = self.config.file.tmp_audio_retention_sec
        
        removed_count = 0
        current_time = time.time()
        
        try:
            if not os.path.exists(self.tmp_audio_dir):
                return 0
            
            for filename in os.listdir(self.tmp_audio_dir):
                file_path = os.path.join(self.tmp_audio_dir, filename)
                
                if not os.path.isfile(file_path):
                    continue
                
                try:
                    file_age = current_time - os.path.getmtime(file_path)
                    if file_age > max_age_seconds:
                        if safe_remove_file(file_path):
                            removed_count += 1
                            logger.debug(f"Removed old audio file: {filename}")
                except OSError as e:
                    logger.warning(f"Could not check file age for {filename}: {e}")
                    continue
            
            if removed_count > 0:
                logger.info(f"Cleaned up {removed_count} old audio files")
            
        except Exception as e:
            logger.error(f"Audio cleanup failed: {e}")
        
        return removed_count
    
    def get_audio_file_path(self, filename: str) -> str:
        """
        Get full path to audio file in temporary directory.
        
        Args:
            filename: Audio filename
            
        Returns:
            Full path to audio file
        """
        return os.path.join(self.tmp_audio_dir, filename)
    
    def validate_audio_file(self, filename: str) -> bool:
        """
        Validate that an audio file exists and is readable.
        
        Args:
            filename: Audio filename
            
        Returns:
            True if file is valid, False otherwise
        """
        file_path = self.get_audio_file_path(filename)
        
        try:
            if not os.path.exists(file_path):
                return False
            
            if not os.path.isfile(file_path):
                return False
            
            if os.path.getsize(file_path) == 0:
                return False
            
            return True
            
        except Exception as e:
            logger.warning(f"Audio file validation failed for {filename}: {e}")
            return False
    
    def get_audio_stats(self) -> dict:
        """
        Get statistics about audio files in temporary directory.
        
        Returns:
            Dictionary with audio file statistics
        """
        stats = {
            'total_files': 0,
            'total_size_bytes': 0,
            'oldest_file_age_seconds': 0,
            'newest_file_age_seconds': 0
        }
        
        try:
            if not os.path.exists(self.tmp_audio_dir):
                return stats
            
            current_time = time.time()
            file_ages = []
            
            for filename in os.listdir(self.tmp_audio_dir):
                file_path = os.path.join(self.tmp_audio_dir, filename)
                
                if os.path.isfile(file_path):
                    stats['total_files'] += 1
                    
                    try:
                        file_size = os.path.getsize(file_path)
                        stats['total_size_bytes'] += file_size
                        
                        file_age = current_time - os.path.getmtime(file_path)
                        file_ages.append(file_age)
                    except OSError:
                        continue
            
            if file_ages:
                stats['oldest_file_age_seconds'] = max(file_ages)
                stats['newest_file_age_seconds'] = min(file_ages)
        
        except Exception as e:
            logger.error(f"Failed to get audio stats: {e}")
        
        return stats
