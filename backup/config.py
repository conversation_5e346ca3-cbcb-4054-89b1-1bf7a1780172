"""
Configuration management module for the chat application.
Centralizes all configuration settings and environment variables.
"""

import os
from typing import List, <PERSON><PERSON>
from dataclasses import dataclass


@dataclass
class KafkaConfig:
    """Kafka configuration settings."""
    brokers: List[str]
    topic: str
    consumer_group: str
    producer_acks: int = 1
    producer_linger_ms: int = 5
    max_request_size: int = 10 * 1024 * 1024
    max_poll_interval_ms: int = 1200000


@dataclass
class RedisConfig:
    """Redis configuration settings."""
    host: str
    port: int
    db: int
    password: str
    max_connections: int = 100


@dataclass
class ExternalAPIConfig:
    """External API configuration settings."""
    tts_url: str
    asr_url: str
    exhibition_api_url: str
    tts_timeout: Tuple[float, float] = (2.0, 10.0)
    asr_timeout: Tuple[float, float] = (2.0, 5.0)
    exhibition_timeout: Tuple[float, float] = (2.0, 5.0)


@dataclass
class FileConfig:
    """File and directory configuration."""
    static_tmp_dir: str
    base_share_dir: str
    tmp_audio_retention_sec: int = 60 * 60 * 12  # 12 hours
    ffmpeg_timeout: int = 15


@dataclass
class AppConfig:
    """Application configuration."""
    host: str = "0.0.0.0"
    port: int = 9214
    secret_key: str = "replace_this_secret"
    log_dir: str = "./logs"
    log_rotation: str = "10 MB"
    log_retention: str = "10 days"
    max_msg_queue_per_client: int = 200
    max_cost_time_records: int = 1000


@dataclass
class Config:
    """Main configuration container."""
    kafka: KafkaConfig
    redis: RedisConfig
    external_api: ExternalAPIConfig
    file: FileConfig
    app: AppConfig


def load_config() -> Config:
    """Load configuration from environment variables with defaults."""
    
    # Kafka configuration
    kafka_brokers = os.getenv("CONFIG_KAFKA", "127.0.0.1:5602").split(",")
    kafka_config = KafkaConfig(
        brokers=kafka_brokers,
        topic=os.getenv("KAFKA_TOPIC", "poc_dev"),
        consumer_group=os.getenv("KAFKA_GROUP", "web_new_002"),
        producer_acks=int(os.getenv("KAFKA_PRODUCER_ACKS", "1")),
        producer_linger_ms=int(os.getenv("KAFKA_PRODUCER_LINGER_MS", "5")),
        max_request_size=int(os.getenv("KAFKA_MAX_REQUEST_SIZE", str(10 * 1024 * 1024))),
        max_poll_interval_ms=int(os.getenv("KAFKA_MAX_POLL_INTERVAL_MS", "1200000"))
    )
    
    # Redis configuration
    redis_config = RedisConfig(
        host=os.getenv("REDIS_HOST", "localhost"),
        port=int(os.getenv("REDIS_PORT", "5601")),
        db=int(os.getenv("REDIS_DB", "0")),
        password=os.getenv("REDIS_PASS", "people@123"),
        max_connections=int(os.getenv("REDIS_MAX_CONNECTIONS", "100"))
    )
    
    # External API configuration
    external_api_config = ExternalAPIConfig(
        tts_url=os.getenv("TTS_URL", "http://**************:8080/f5tts/tts"),
        asr_url=os.getenv("ASR_URL", "http://127.0.0.1:8110/asr/asr_offline"),
        exhibition_api_url=os.getenv("EXHIBITION_API_URL", "http://127.0.0.1:5606/get_exhibition_LatLon"),
        tts_timeout=(
            float(os.getenv("TTS_TIMEOUT_CONNECT", "2.0")),
            float(os.getenv("TTS_TIMEOUT_READ", "10.0"))
        ),
        asr_timeout=(
            float(os.getenv("ASR_TIMEOUT_CONNECT", "2.0")),
            float(os.getenv("ASR_TIMEOUT_READ", "5.0"))
        ),
        exhibition_timeout=(
            float(os.getenv("EXHIBITION_TIMEOUT_CONNECT", "2.0")),
            float(os.getenv("EXHIBITION_TIMEOUT_READ", "5.0"))
        )
    )
    
    # File configuration
    static_tmp_dir = os.getenv("STATIC_TMP", "static/tmp")
    file_config = FileConfig(
        static_tmp_dir=static_tmp_dir,
        base_share_dir=os.getenv("BASE_SHARE_DIR", "/private/share/chat"),
        tmp_audio_retention_sec=int(os.getenv("TMP_AUDIO_RETENTION_SEC", str(60*60*12))),
        ffmpeg_timeout=int(os.getenv("FFMPEG_TIMEOUT", "15"))
    )
    
    # App configuration
    app_config = AppConfig(
        host=os.getenv("APP_HOST", "0.0.0.0"),
        port=int(os.getenv("APP_PORT", "9214")), #9214
        secret_key=os.getenv("FLASK_SECRET", "replace_this_secret"),
        log_dir=os.getenv("LOG_DIR", "./logs"),
        log_rotation=os.getenv("LOG_ROTATION", "10 MB"),
        log_retention=os.getenv("LOG_RETENTION", "10 days"),
        max_msg_queue_per_client=int(os.getenv("MAX_MSG_QUEUE_PER_CLIENT", "200")),
        max_cost_time_records=int(os.getenv("MAX_COST_TIME_RECORDS", "1000"))
    )
    
    return Config(
        kafka=kafka_config,
        redis=redis_config,
        external_api=external_api_config,
        file=file_config,
        app=app_config
    )


# Global configuration instance
config = load_config()
