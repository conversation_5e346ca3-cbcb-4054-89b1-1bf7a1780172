2025-09-01 17:43:49 | INFO     | __main__:_setup_logging:71 - Logging configured successfully
2025-09-01 17:43:49 | INFO     | __main__:run:225 - Starting chat application...
2025-09-01 17:43:49 | INFO     | __main__:run:226 - Configuration: Kafka=['127.0.0.1:5602'], Redis=localhost:5601
2025-09-01 17:43:49 | INFO     | __main__:_validate_environment:110 - Created directory: static/tmp
2025-09-01 17:43:49 | INFO     | __main__:_validate_environment:120 - FFmpeg is available
2025-09-01 17:43:49 | INFO     | __main__:_validate_environment:133 - Environment validation passed
2025-09-01 17:43:49 | INFO     | __main__:_create_application:143 - Creating chat application...
2025-09-01 17:43:49 | INFO     | services:initialize:47 - Redis connected to localhost:5601
2025-09-01 17:43:49 | INFO     | services:initialize:79 - <PERSON><PERSON><PERSON> producer initialized for brokers: ['127.0.0.1:5602']
2025-09-01 17:43:49 | INFO     | services:initialize:192 - External API service initialized
2025-09-01 17:43:49 | INFO     | services:initialize_all:391 - All services initialized successfully
2025-09-01 17:43:49 | INFO     | app:initialize:88 - Chat application initialized successfully
2025-09-01 17:43:49 | INFO     | message_handler:start_kafka_consumer:184 - Kafka consumer started
2025-09-01 17:43:49 | INFO     | task_manager:start_workers:88 - Started 3 task workers
2025-09-01 17:43:49 | INFO     | app:start_background_tasks:107 - Background tasks started
2025-09-01 17:43:49 | INFO     | __main__:_create_application:149 - Chat application created successfully
2025-09-01 17:43:49 | INFO     | __main__:_start_server:159 - Starting server on 0.0.0.0:9214
2025-09-01 17:43:49 | INFO     | __main__:_start_server:168 - Server started successfully on http://0.0.0.0:9214
2025-09-01 17:43:49 | INFO     | __main__:_start_server:169 - Application is ready to accept connections
2025-09-01 17:43:49 | INFO     | task_manager:_worker_loop:163 - Task worker worker-0 started
2025-09-01 17:43:49 | INFO     | task_manager:_worker_loop:163 - Task worker worker-1 started
2025-09-01 17:43:49 | INFO     | task_manager:_worker_loop:163 - Task worker worker-2 started
2025-09-01 17:43:49 | DEBUG    | app:_cleanup_loop:335 - Cleanup cycle completed
2025-09-01 17:43:49 | INFO     | message_handler:_consume_messages:197 - Kafka consumer initialized successfully
2025-09-01 17:53:50 | DEBUG    | app:_cleanup_loop:335 - Cleanup cycle completed
2025-09-01 18:03:50 | DEBUG    | app:_cleanup_loop:335 - Cleanup cycle completed
2025-09-01 18:06:13 | INFO     | __main__:signal_handler:81 - Received SIGINT signal, initiating graceful shutdown...
2025-09-01 18:06:13 | INFO     | __main__:_shutdown:187 - Starting graceful shutdown...
2025-09-01 18:06:13 | INFO     | __main__:_shutdown:191 - Stopping WSGI server...
2025-09-01 18:06:13 | INFO     | __main__:_shutdown:197 - Shutting down application...
2025-09-01 18:06:13 | INFO     | app:shutdown:115 - Shutting down chat application...
2025-09-02 13:14:29 | INFO     | __main__:_setup_logging:71 - Logging configured successfully
2025-09-02 13:14:29 | INFO     | __main__:run:225 - Starting chat application...
2025-09-02 13:14:29 | INFO     | __main__:run:226 - Configuration: Kafka=['127.0.0.1:5602'], Redis=localhost:5601
2025-09-02 13:14:29 | INFO     | __main__:_validate_environment:120 - FFmpeg is available
2025-09-02 13:14:29 | INFO     | __main__:_validate_environment:133 - Environment validation passed
2025-09-02 13:14:29 | INFO     | __main__:_create_application:143 - Creating chat application...
2025-09-02 13:14:29 | INFO     | services:initialize:47 - Redis connected to localhost:5601
2025-09-02 13:14:29 | INFO     | services:initialize:79 - Kafka producer initialized for brokers: ['127.0.0.1:5602']
2025-09-02 13:14:29 | INFO     | services:initialize:192 - External API service initialized
2025-09-02 13:14:29 | INFO     | services:initialize_all:391 - All services initialized successfully
2025-09-02 13:14:29 | INFO     | app:initialize:88 - Chat application initialized successfully
2025-09-02 13:14:29 | INFO     | message_handler:start_kafka_consumer:184 - Kafka consumer started
2025-09-02 13:14:29 | INFO     | task_manager:start_workers:88 - Started 3 task workers
2025-09-02 13:14:29 | INFO     | app:start_background_tasks:107 - Background tasks started
2025-09-02 13:14:29 | INFO     | __main__:_create_application:149 - Chat application created successfully
2025-09-02 13:14:29 | INFO     | __main__:_start_server:159 - Starting server on 0.0.0.0:9214
2025-09-02 13:14:29 | INFO     | __main__:_start_server:168 - Server started successfully on http://0.0.0.0:9214
2025-09-02 13:14:29 | INFO     | __main__:_start_server:169 - Application is ready to accept connections
2025-09-02 13:14:29 | INFO     | task_manager:_worker_loop:163 - Task worker worker-0 started
2025-09-02 13:14:29 | INFO     | task_manager:_worker_loop:163 - Task worker worker-1 started
2025-09-02 13:14:29 | INFO     | task_manager:_worker_loop:163 - Task worker worker-2 started
2025-09-02 13:14:29 | DEBUG    | app:_cleanup_loop:335 - Cleanup cycle completed
2025-09-02 13:14:30 | INFO     | message_handler:_consume_messages:197 - Kafka consumer initialized successfully
2025-09-02 13:20:10 | INFO     | message_handler:add_client:41 - New client connected: test_device_legacy_123
2025-09-02 13:20:10 | DEBUG    | message_handler:handle_websocket_message:406 - Received WebSocket message from test_device_legacy_123: query
2025-09-02 13:20:10 | INFO     | message_handler:handle_websocket_message:409 - Query message received from test_device_legacy_123
2025-09-02 13:20:10 | DEBUG    | app:_handle_websocket:307 - Processed WebSocket message from test_device_legacy_123: query
2025-09-02 13:20:11 | INFO     | task_manager:submit_upload_task:125 - Submitted upload task: test_device_legacy_123
2025-09-02 13:20:11 | INFO     | app:_handle_upload:197 - Upload task submitted for device test_device_legacy_123
2025-09-02 13:20:11 | INFO     | task_manager:_process_task:190 - Worker worker-0 processing task test_device_legacy_123
2025-09-02 13:20:11 | INFO     | task_manager:_store_user_context:302 - Setting exhibition info for test_device_legacy_123: 53, 人民网综合展厅
2025-09-02 13:20:11 | DEBUG    | audio_processor:convert_to_16khz_mono:61 - Audio conversion successful: /private/share/chat/test_device_legacy_123_16000.wav
2025-09-02 13:20:11 | DEBUG    | utils:__exit__:269 - FFmpeg conversion: test_device_legacy_123.wav completed in 0.049s
2025-09-02 13:20:11 | DEBUG    | utils:__exit__:269 - ASR request for 2078 bytes completed in 0.005s
2025-09-02 13:20:11 | DEBUG    | utils:safe_remove_file:90 - Removed file: /private/share/chat/test_device_legacy_123.wav
2025-09-02 13:20:11 | DEBUG    | utils:safe_remove_file:90 - Removed file: /private/share/chat/test_device_legacy_123_16000.wav
2025-09-02 13:20:11 | WARNING  | audio_processor:process_base64_audio:196 - ASR returned no text
2025-09-02 13:20:11 | INFO     | image_processor:process_base64_image:91 - Image saved for device test_device_legacy_123: /private/share/chat/68b8959709675e69d4ca457fbcaa7717.png (84 bytes)
2025-09-02 13:20:11 | INFO     | task_manager:_process_upload_task:271 - Image saved for test_device_legacy_123: /private/share/chat/68b8959709675e69d4ca457fbcaa7717.png
2025-09-02 13:20:11 | DEBUG    | services:send_message:122 - Kafka message sent: {"frame_time": 1756790411.14, "longitude": 0, "latitude": 0, "degree": 0, "ts": 0, "device_id": "tes...
2025-09-02 13:20:11 | INFO     | task_manager:_send_processing_results:362 - Sent image to image_caption: /private/share/chat/68b8959709675e69d4ca457fbcaa7717.png
2025-09-02 13:20:11 | DEBUG    | services:send_message:122 - Kafka message sent: {"frame_time": 1756790411.14, "longitude": 0, "latitude": 0, "degree": 0, "ts": 0, "device_id": "tes...
2025-09-02 13:20:11 | INFO     | task_manager:_send_processing_results:389 - Sent trigger message for combined processing
2025-09-02 13:20:11 | INFO     | task_manager:_process_task:213 - Task test_device_legacy_123 completed successfully
2025-09-02 13:20:11 | DEBUG    | message_handler:_process_kafka_message:226 - Received Kafka message: {'frame_time': 1756790411.14, 'longitude': 0, 'latitude': 0, 'degree': 0, 'ts': 0, 'device_id': 'test_device_legacy_123', 'exhibition_id': 53, 'user_id': 'user0', 'event_id': 'test_device_legacy_123', 'context': '', 'device_type': 'phone', 'from': 'client', 'frame': '/private/share/chat/68b8959709675e69d4ca457fbcaa7717.png', 'to': 'image_caption', 'interface': 'image_caption'}
2025-09-02 13:20:11 | DEBUG    | message_handler:_process_kafka_message:252 - Ignoring Kafka message with 'to': image_caption
2025-09-02 13:20:11 | DEBUG    | message_handler:_process_kafka_message:226 - Received Kafka message: {'frame_time': 1756790411.14, 'longitude': 0, 'latitude': 0, 'degree': 0, 'ts': 0, 'device_id': 'test_device_legacy_123', 'exhibition_id': 53, 'user_id': 'user0', 'event_id': 'test_device_legacy_123', 'context': '', 'device_type': 'phone', 'from': 'client', 'frame': '/private/share/chat/68b8959709675e69d4ca457fbcaa7717.png', 'query': '', 'to': 'trigger', 'interface': 'trigger'}
2025-09-02 13:20:11 | DEBUG    | message_handler:_process_kafka_message:252 - Ignoring Kafka message with 'to': trigger
2025-09-02 13:20:21 | INFO     | message_handler:remove_client:67 - Client disconnected: test_device_legacy_123
2025-09-02 13:21:46 | INFO     | __main__:signal_handler:81 - Received SIGINT signal, initiating graceful shutdown...
2025-09-02 13:21:46 | INFO     | __main__:_shutdown:187 - Starting graceful shutdown...
2025-09-02 13:21:46 | INFO     | __main__:_shutdown:191 - Stopping WSGI server...
2025-09-02 13:21:46 | INFO     | __main__:_shutdown:197 - Shutting down application...
2025-09-02 13:21:46 | INFO     | app:shutdown:115 - Shutting down chat application...
2025-09-02 13:21:53 | INFO     | __main__:_setup_logging:71 - Logging configured successfully
2025-09-02 13:21:53 | INFO     | __main__:run:225 - Starting chat application...
2025-09-02 13:21:53 | INFO     | __main__:run:226 - Configuration: Kafka=['127.0.0.1:5602'], Redis=localhost:5601
2025-09-02 13:21:53 | INFO     | __main__:_validate_environment:120 - FFmpeg is available
2025-09-02 13:21:53 | INFO     | __main__:_validate_environment:133 - Environment validation passed
2025-09-02 13:21:53 | INFO     | __main__:_create_application:143 - Creating chat application...
2025-09-02 13:21:53 | INFO     | services:initialize:47 - Redis connected to localhost:5601
2025-09-02 13:21:53 | INFO     | services:initialize:79 - Kafka producer initialized for brokers: ['127.0.0.1:5602']
2025-09-02 13:21:53 | INFO     | services:initialize:192 - External API service initialized
2025-09-02 13:21:53 | INFO     | services:initialize_all:391 - All services initialized successfully
2025-09-02 13:21:53 | INFO     | app:initialize:88 - Chat application initialized successfully
2025-09-02 13:21:53 | INFO     | message_handler:start_kafka_consumer:184 - Kafka consumer started
2025-09-02 13:21:53 | INFO     | task_manager:start_workers:88 - Started 3 task workers
2025-09-02 13:21:53 | INFO     | app:start_background_tasks:107 - Background tasks started
2025-09-02 13:21:53 | INFO     | __main__:_create_application:149 - Chat application created successfully
2025-09-02 13:21:53 | INFO     | __main__:_start_server:159 - Starting server on 0.0.0.0:9214
2025-09-02 13:21:53 | INFO     | __main__:_start_server:168 - Server started successfully on http://0.0.0.0:9214
2025-09-02 13:21:53 | INFO     | __main__:_start_server:169 - Application is ready to accept connections
2025-09-02 13:21:53 | INFO     | task_manager:_worker_loop:163 - Task worker worker-0 started
2025-09-02 13:21:53 | INFO     | task_manager:_worker_loop:163 - Task worker worker-1 started
2025-09-02 13:21:53 | INFO     | task_manager:_worker_loop:163 - Task worker worker-2 started
2025-09-02 13:21:53 | DEBUG    | app:_cleanup_loop:335 - Cleanup cycle completed
2025-09-02 13:21:54 | INFO     | message_handler:_consume_messages:197 - Kafka consumer initialized successfully
2025-09-02 13:22:33 | INFO     | message_handler:add_client:41 - New client connected: test_device_legacy_123
2025-09-02 13:22:33 | DEBUG    | message_handler:handle_websocket_message:406 - Received WebSocket message from test_device_legacy_123: query
2025-09-02 13:22:33 | INFO     | message_handler:handle_websocket_message:409 - Query message received from test_device_legacy_123
2025-09-02 13:22:33 | DEBUG    | app:_handle_websocket:307 - Processed WebSocket message from test_device_legacy_123: query
2025-09-02 13:22:33 | INFO     | task_manager:submit_upload_task:125 - Submitted upload task: test_device_legacy_123
2025-09-02 13:22:33 | INFO     | app:_handle_upload:197 - Upload task submitted for device test_device_legacy_123
2025-09-02 13:22:33 | INFO     | task_manager:_process_task:190 - Worker worker-0 processing task test_device_legacy_123
2025-09-02 13:22:33 | INFO     | task_manager:_store_user_context:302 - Setting exhibition info for test_device_legacy_123: 53, 人民网综合展厅
2025-09-02 13:22:33 | DEBUG    | audio_processor:convert_to_16khz_mono:61 - Audio conversion successful: /private/share/chat/test_device_legacy_123_16000.wav
2025-09-02 13:22:33 | DEBUG    | utils:__exit__:269 - FFmpeg conversion: test_device_legacy_123.wav completed in 0.048s
2025-09-02 13:22:33 | DEBUG    | utils:__exit__:269 - ASR request for 2078 bytes completed in 0.005s
2025-09-02 13:22:33 | DEBUG    | utils:safe_remove_file:90 - Removed file: /private/share/chat/test_device_legacy_123.wav
2025-09-02 13:22:33 | DEBUG    | utils:safe_remove_file:90 - Removed file: /private/share/chat/test_device_legacy_123_16000.wav
2025-09-02 13:22:33 | WARNING  | audio_processor:process_base64_audio:196 - ASR returned no text
2025-09-02 13:22:33 | INFO     | image_processor:process_base64_image:91 - Image saved for device test_device_legacy_123: /private/share/chat/19df894aa44b408c778034567ebbf7e7.png (84 bytes)
2025-09-02 13:22:33 | INFO     | task_manager:_process_upload_task:271 - Image saved for test_device_legacy_123: /private/share/chat/19df894aa44b408c778034567ebbf7e7.png
2025-09-02 13:22:33 | DEBUG    | services:send_message:122 - Kafka message sent: {"frame_time": 1756790553.26, "longitude": 0, "latitude": 0, "degree": 0, "ts": 0, "device_id": "tes...
2025-09-02 13:22:33 | INFO     | task_manager:_send_processing_results:362 - Sent image to image_caption: /private/share/chat/19df894aa44b408c778034567ebbf7e7.png
2025-09-02 13:22:33 | DEBUG    | services:send_message:122 - Kafka message sent: {"frame_time": 1756790553.26, "longitude": 0, "latitude": 0, "degree": 0, "ts": 0, "device_id": "tes...
2025-09-02 13:22:33 | INFO     | task_manager:_send_processing_results:389 - Sent trigger message for combined processing
2025-09-02 13:22:33 | INFO     | task_manager:_process_task:213 - Task test_device_legacy_123 completed successfully
2025-09-02 13:22:33 | DEBUG    | message_handler:_process_kafka_message:226 - Received Kafka message: {'frame_time': 1756790553.26, 'longitude': 0, 'latitude': 0, 'degree': 0, 'ts': 0, 'device_id': 'test_device_legacy_123', 'exhibition_id': 53, 'user_id': 'user0', 'event_id': 'test_device_legacy_123', 'context': '', 'device_type': 'phone', 'from': 'client', 'frame': '/private/share/chat/19df894aa44b408c778034567ebbf7e7.png', 'to': 'image_caption', 'interface': 'image_caption'}
2025-09-02 13:22:33 | DEBUG    | message_handler:_process_kafka_message:252 - Ignoring Kafka message with 'to': image_caption
2025-09-02 13:22:33 | DEBUG    | message_handler:_process_kafka_message:226 - Received Kafka message: {'frame_time': 1756790553.26, 'longitude': 0, 'latitude': 0, 'degree': 0, 'ts': 0, 'device_id': 'test_device_legacy_123', 'exhibition_id': 53, 'user_id': 'user0', 'event_id': 'test_device_legacy_123', 'context': '', 'device_type': 'phone', 'from': 'client', 'frame': '/private/share/chat/19df894aa44b408c778034567ebbf7e7.png', 'query': '', 'to': 'trigger', 'interface': 'trigger'}
2025-09-02 13:22:33 | DEBUG    | message_handler:_process_kafka_message:252 - Ignoring Kafka message with 'to': trigger
2025-09-02 13:22:43 | INFO     | message_handler:remove_client:67 - Client disconnected: test_device_legacy_123
2025-09-02 13:25:02 | INFO     | message_handler:add_client:41 - New client connected: test_device_legacy_123
2025-09-02 13:25:02 | DEBUG    | message_handler:handle_websocket_message:406 - Received WebSocket message from test_device_legacy_123: query
2025-09-02 13:25:02 | INFO     | message_handler:handle_websocket_message:409 - Query message received from test_device_legacy_123
2025-09-02 13:25:02 | DEBUG    | app:_handle_websocket:307 - Processed WebSocket message from test_device_legacy_123: query
2025-09-02 13:25:02 | INFO     | task_manager:submit_upload_task:125 - Submitted upload task: test_device_legacy_123
2025-09-02 13:25:02 | INFO     | app:_handle_upload:197 - Upload task submitted for device test_device_legacy_123
2025-09-02 13:25:02 | INFO     | task_manager:_process_task:190 - Worker worker-1 processing task test_device_legacy_123
2025-09-02 13:25:02 | INFO     | task_manager:_store_user_context:302 - Setting exhibition info for test_device_legacy_123: 53, 人民网综合展厅
2025-09-02 13:25:02 | DEBUG    | audio_processor:convert_to_16khz_mono:61 - Audio conversion successful: /private/share/chat/test_device_legacy_123_16000.wav
2025-09-02 13:25:02 | DEBUG    | utils:__exit__:269 - FFmpeg conversion: test_device_legacy_123.wav completed in 0.049s
2025-09-02 13:25:02 | DEBUG    | utils:__exit__:269 - ASR request for 2078 bytes completed in 0.005s
2025-09-02 13:25:02 | DEBUG    | utils:safe_remove_file:90 - Removed file: /private/share/chat/test_device_legacy_123.wav
2025-09-02 13:25:02 | DEBUG    | utils:safe_remove_file:90 - Removed file: /private/share/chat/test_device_legacy_123_16000.wav
2025-09-02 13:25:02 | WARNING  | audio_processor:process_base64_audio:196 - ASR returned no text
2025-09-02 13:25:02 | INFO     | image_processor:process_base64_image:91 - Image saved for device test_device_legacy_123: /private/share/chat/8494a99f9544e8dc6ebec0fd1b970d31.png (84 bytes)
2025-09-02 13:25:02 | INFO     | task_manager:_process_upload_task:271 - Image saved for test_device_legacy_123: /private/share/chat/8494a99f9544e8dc6ebec0fd1b970d31.png
2025-09-02 13:25:02 | DEBUG    | services:send_message:122 - Kafka message sent: {"frame_time": 1756790702.75, "longitude": 0, "latitude": 0, "degree": 0, "ts": 0, "device_id": "tes...
2025-09-02 13:25:02 | INFO     | task_manager:_send_processing_results:362 - Sent image to image_caption: /private/share/chat/8494a99f9544e8dc6ebec0fd1b970d31.png
2025-09-02 13:25:02 | DEBUG    | services:send_message:122 - Kafka message sent: {"frame_time": 1756790702.75, "longitude": 0, "latitude": 0, "degree": 0, "ts": 0, "device_id": "tes...
2025-09-02 13:25:02 | INFO     | task_manager:_send_processing_results:389 - Sent trigger message for combined processing
2025-09-02 13:25:02 | INFO     | task_manager:_process_task:213 - Task test_device_legacy_123 completed successfully
2025-09-02 13:25:02 | DEBUG    | message_handler:_process_kafka_message:226 - Received Kafka message: {'frame_time': 1756790702.75, 'longitude': 0, 'latitude': 0, 'degree': 0, 'ts': 0, 'device_id': 'test_device_legacy_123', 'exhibition_id': 53, 'user_id': 'user0', 'event_id': 'test_device_legacy_123', 'context': '', 'device_type': 'phone', 'from': 'client', 'frame': '/private/share/chat/8494a99f9544e8dc6ebec0fd1b970d31.png', 'to': 'image_caption', 'interface': 'image_caption'}
2025-09-02 13:25:02 | DEBUG    | message_handler:_process_kafka_message:252 - Ignoring Kafka message with 'to': image_caption
2025-09-02 13:25:02 | DEBUG    | message_handler:_process_kafka_message:226 - Received Kafka message: {'frame_time': 1756790702.75, 'longitude': 0, 'latitude': 0, 'degree': 0, 'ts': 0, 'device_id': 'test_device_legacy_123', 'exhibition_id': 53, 'user_id': 'user0', 'event_id': 'test_device_legacy_123', 'context': '', 'device_type': 'phone', 'from': 'client', 'frame': '/private/share/chat/8494a99f9544e8dc6ebec0fd1b970d31.png', 'query': '', 'to': 'trigger', 'interface': 'trigger'}
2025-09-02 13:25:02 | DEBUG    | message_handler:_process_kafka_message:252 - Ignoring Kafka message with 'to': trigger
2025-09-02 13:25:12 | INFO     | message_handler:remove_client:67 - Client disconnected: test_device_legacy_123
2025-09-02 13:31:55 | DEBUG    | app:_cleanup_loop:335 - Cleanup cycle completed
2025-09-02 13:37:07 | INFO     | __main__:signal_handler:81 - Received SIGINT signal, initiating graceful shutdown...
2025-09-02 13:37:07 | INFO     | __main__:_shutdown:187 - Starting graceful shutdown...
2025-09-02 13:37:07 | INFO     | __main__:_shutdown:191 - Stopping WSGI server...
2025-09-02 13:37:07 | INFO     | __main__:_shutdown:197 - Shutting down application...
2025-09-02 13:37:07 | INFO     | app:shutdown:115 - Shutting down chat application...
2025-09-02 13:37:09 | INFO     | __main__:_setup_logging:71 - Logging configured successfully
2025-09-02 13:37:09 | INFO     | __main__:run:225 - Starting chat application...
2025-09-02 13:37:09 | INFO     | __main__:run:226 - Configuration: Kafka=['127.0.0.1:5602'], Redis=localhost:5601
2025-09-02 13:37:09 | INFO     | __main__:_validate_environment:120 - FFmpeg is available
2025-09-02 13:37:09 | INFO     | __main__:_validate_environment:133 - Environment validation passed
2025-09-02 13:37:09 | INFO     | __main__:_create_application:143 - Creating chat application...
2025-09-02 13:37:09 | INFO     | services:initialize:47 - Redis connected to localhost:5601
2025-09-02 13:37:09 | INFO     | services:initialize:79 - Kafka producer initialized for brokers: ['127.0.0.1:5602']
2025-09-02 13:37:09 | INFO     | services:initialize:192 - External API service initialized
2025-09-02 13:37:09 | INFO     | services:initialize_all:391 - All services initialized successfully
2025-09-02 13:37:09 | INFO     | app:initialize:88 - Chat application initialized successfully
2025-09-02 13:37:09 | INFO     | message_handler:start_kafka_consumer:184 - Kafka consumer started
2025-09-02 13:37:09 | INFO     | task_manager:start_workers:88 - Started 3 task workers
2025-09-02 13:37:09 | INFO     | app:start_background_tasks:107 - Background tasks started
2025-09-02 13:37:09 | INFO     | __main__:_create_application:149 - Chat application created successfully
2025-09-02 13:37:09 | INFO     | __main__:_start_server:159 - Starting server on 0.0.0.0:9214
2025-09-02 13:37:09 | INFO     | __main__:_start_server:168 - Server started successfully on http://0.0.0.0:9214
2025-09-02 13:37:09 | INFO     | __main__:_start_server:169 - Application is ready to accept connections
2025-09-02 13:37:09 | INFO     | task_manager:_worker_loop:163 - Task worker worker-0 started
2025-09-02 13:37:09 | INFO     | task_manager:_worker_loop:163 - Task worker worker-1 started
2025-09-02 13:37:09 | INFO     | task_manager:_worker_loop:163 - Task worker worker-2 started
2025-09-02 13:37:09 | DEBUG    | app:_cleanup_loop:336 - Cleanup cycle completed
2025-09-02 13:37:10 | INFO     | message_handler:_consume_messages:197 - Kafka consumer initialized successfully
2025-09-02 13:37:13 | INFO     | message_handler:add_client:41 - New client connected: test_device_legacy_123
2025-09-02 13:37:13 | DEBUG    | message_handler:handle_websocket_message:406 - Received WebSocket message from test_device_legacy_123: query
2025-09-02 13:37:13 | INFO     | message_handler:handle_websocket_message:409 - Query message received from test_device_legacy_123
2025-09-02 13:37:13 | DEBUG    | app:_handle_websocket:308 - Processed WebSocket message from test_device_legacy_123: query
2025-09-02 13:37:13 | INFO     | task_manager:submit_upload_task:125 - Submitted upload task: test_device_legacy_123
2025-09-02 13:37:13 | INFO     | app:_handle_upload:197 - Upload task submitted for device test_device_legacy_123
2025-09-02 13:37:13 | INFO     | task_manager:_process_task:190 - Worker worker-0 processing task test_device_legacy_123
2025-09-02 13:37:13 | INFO     | task_manager:_store_user_context:302 - Setting exhibition info for test_device_legacy_123: 53, 人民网综合展厅
2025-09-02 13:37:13 | DEBUG    | audio_processor:convert_to_16khz_mono:61 - Audio conversion successful: /private/share/chat/test_device_legacy_123_16000.wav
2025-09-02 13:37:13 | DEBUG    | utils:__exit__:269 - FFmpeg conversion: test_device_legacy_123.wav completed in 0.051s
2025-09-02 13:37:13 | DEBUG    | utils:__exit__:269 - ASR request for 2078 bytes completed in 0.005s
2025-09-02 13:37:13 | DEBUG    | utils:safe_remove_file:90 - Removed file: /private/share/chat/test_device_legacy_123.wav
2025-09-02 13:37:13 | DEBUG    | utils:safe_remove_file:90 - Removed file: /private/share/chat/test_device_legacy_123_16000.wav
2025-09-02 13:37:13 | WARNING  | audio_processor:process_base64_audio:196 - ASR returned no text
2025-09-02 13:37:13 | INFO     | image_processor:process_base64_image:91 - Image saved for device test_device_legacy_123: /private/share/chat/507e415c41f390906f5ba653479a857d.png (84 bytes)
2025-09-02 13:37:13 | INFO     | task_manager:_process_upload_task:271 - Image saved for test_device_legacy_123: /private/share/chat/507e415c41f390906f5ba653479a857d.png
2025-09-02 13:37:13 | DEBUG    | services:send_message:122 - Kafka message sent: {"frame_time": 1756791433.47, "longitude": 0, "latitude": 0, "degree": 0, "ts": 0, "device_id": "tes...
2025-09-02 13:37:13 | INFO     | task_manager:_send_processing_results:362 - Sent image to image_caption: /private/share/chat/507e415c41f390906f5ba653479a857d.png
2025-09-02 13:37:13 | DEBUG    | services:send_message:122 - Kafka message sent: {"frame_time": 1756791433.47, "longitude": 0, "latitude": 0, "degree": 0, "ts": 0, "device_id": "tes...
2025-09-02 13:37:13 | INFO     | task_manager:_send_processing_results:389 - Sent trigger message for combined processing
2025-09-02 13:37:13 | INFO     | task_manager:_process_task:213 - Task test_device_legacy_123 completed successfully
2025-09-02 13:37:13 | DEBUG    | message_handler:_process_kafka_message:226 - Received Kafka message: {'frame_time': 1756791433.47, 'longitude': 0, 'latitude': 0, 'degree': 0, 'ts': 0, 'device_id': 'test_device_legacy_123', 'exhibition_id': 53, 'user_id': 'user0', 'event_id': 'test_device_legacy_123', 'context': '', 'device_type': 'phone', 'from': 'client', 'frame': '/private/share/chat/507e415c41f390906f5ba653479a857d.png', 'to': 'image_caption', 'interface': 'image_caption'}
2025-09-02 13:37:13 | DEBUG    | message_handler:_process_kafka_message:252 - Ignoring Kafka message with 'to': image_caption
2025-09-02 13:37:13 | DEBUG    | message_handler:_process_kafka_message:226 - Received Kafka message: {'frame_time': 1756791433.47, 'longitude': 0, 'latitude': 0, 'degree': 0, 'ts': 0, 'device_id': 'test_device_legacy_123', 'exhibition_id': 53, 'user_id': 'user0', 'event_id': 'test_device_legacy_123', 'context': '', 'device_type': 'phone', 'from': 'client', 'frame': '/private/share/chat/507e415c41f390906f5ba653479a857d.png', 'query': '', 'to': 'trigger', 'interface': 'trigger'}
2025-09-02 13:37:13 | DEBUG    | message_handler:_process_kafka_message:252 - Ignoring Kafka message with 'to': trigger
2025-09-02 13:37:23 | INFO     | message_handler:remove_client:67 - Client disconnected: test_device_legacy_123
2025-09-02 13:43:21 | INFO     | __main__:signal_handler:81 - Received SIGINT signal, initiating graceful shutdown...
2025-09-02 13:43:21 | INFO     | __main__:_shutdown:187 - Starting graceful shutdown...
2025-09-02 13:43:21 | INFO     | __main__:_shutdown:191 - Stopping WSGI server...
2025-09-02 13:43:21 | INFO     | __main__:_shutdown:197 - Shutting down application...
2025-09-02 13:43:21 | INFO     | app:shutdown:115 - Shutting down chat application...
2025-09-02 13:43:23 | INFO     | __main__:_setup_logging:71 - Logging configured successfully
2025-09-02 13:43:23 | INFO     | __main__:run:225 - Starting chat application...
2025-09-02 13:43:23 | INFO     | __main__:run:226 - Configuration: Kafka=['127.0.0.1:5602'], Redis=localhost:5601
2025-09-02 13:43:23 | INFO     | __main__:_validate_environment:120 - FFmpeg is available
2025-09-02 13:43:23 | INFO     | __main__:_validate_environment:133 - Environment validation passed
2025-09-02 13:43:23 | INFO     | __main__:_create_application:143 - Creating chat application...
2025-09-02 13:43:23 | INFO     | services:initialize:47 - Redis connected to localhost:5601
2025-09-02 13:43:23 | INFO     | services:initialize:79 - Kafka producer initialized for brokers: ['127.0.0.1:5602']
2025-09-02 13:43:23 | INFO     | services:initialize:192 - External API service initialized
2025-09-02 13:43:23 | INFO     | services:initialize_all:391 - All services initialized successfully
2025-09-02 13:43:23 | INFO     | app:initialize:88 - Chat application initialized successfully
2025-09-02 13:43:23 | INFO     | message_handler:start_kafka_consumer:184 - Kafka consumer started
2025-09-02 13:43:23 | INFO     | task_manager:start_workers:88 - Started 3 task workers
2025-09-02 13:43:23 | INFO     | app:start_background_tasks:107 - Background tasks started
2025-09-02 13:43:23 | INFO     | __main__:_create_application:149 - Chat application created successfully
2025-09-02 13:43:23 | INFO     | __main__:_start_server:159 - Starting server on 0.0.0.0:9214
2025-09-02 13:43:23 | INFO     | __main__:_start_server:168 - Server started successfully on http://0.0.0.0:9214
2025-09-02 13:43:23 | INFO     | __main__:_start_server:169 - Application is ready to accept connections
2025-09-02 13:43:23 | INFO     | task_manager:_worker_loop:163 - Task worker worker-0 started
2025-09-02 13:43:23 | INFO     | task_manager:_worker_loop:163 - Task worker worker-1 started
2025-09-02 13:43:23 | INFO     | task_manager:_worker_loop:163 - Task worker worker-2 started
2025-09-02 13:43:24 | DEBUG    | app:_cleanup_loop:336 - Cleanup cycle completed
2025-09-02 13:43:24 | INFO     | message_handler:_consume_messages:197 - Kafka consumer initialized successfully
2025-09-02 13:43:26 | INFO     | message_handler:add_client:41 - New client connected: test_device_legacy_123
2025-09-02 13:43:26 | DEBUG    | message_handler:handle_websocket_message:406 - Received WebSocket message from test_device_legacy_123: query
2025-09-02 13:43:26 | INFO     | message_handler:handle_websocket_message:409 - Query message received from test_device_legacy_123
2025-09-02 13:43:26 | DEBUG    | app:_handle_websocket:308 - Processed WebSocket message from test_device_legacy_123: query
2025-09-02 13:43:26 | INFO     | app:_handle_upload:166 - Received upload request from test_device_legacy_123
2025-09-02 13:43:26 | INFO     | task_manager:submit_upload_task:125 - Submitted upload task: test_device_legacy_123
2025-09-02 13:43:26 | INFO     | app:_handle_upload:197 - Upload task submitted for device test_device_legacy_123
2025-09-02 13:43:26 | INFO     | task_manager:_process_task:190 - Worker worker-0 processing task test_device_legacy_123
2025-09-02 13:43:26 | INFO     | task_manager:_process_task:199 - Processing upload task test_device_legacy_123
2025-09-02 13:43:26 | INFO     | task_manager:_store_user_context:303 - Setting exhibition info for test_device_legacy_123: 53, 人民网综合展厅
2025-09-02 13:43:26 | DEBUG    | audio_processor:convert_to_16khz_mono:61 - Audio conversion successful: /private/share/chat/test_device_legacy_123_16000.wav
2025-09-02 13:43:26 | DEBUG    | utils:__exit__:269 - FFmpeg conversion: test_device_legacy_123.wav completed in 0.049s
2025-09-02 13:43:26 | DEBUG    | utils:__exit__:269 - ASR request for 2078 bytes completed in 0.004s
2025-09-02 13:43:26 | DEBUG    | utils:safe_remove_file:90 - Removed file: /private/share/chat/test_device_legacy_123.wav
2025-09-02 13:43:26 | DEBUG    | utils:safe_remove_file:90 - Removed file: /private/share/chat/test_device_legacy_123_16000.wav
2025-09-02 13:43:26 | WARNING  | audio_processor:process_base64_audio:196 - ASR returned no text
2025-09-02 13:43:26 | INFO     | image_processor:process_base64_image:91 - Image saved for device test_device_legacy_123: /private/share/chat/19c42472576ba631a6d1d859c2f1b867.png (84 bytes)
2025-09-02 13:43:26 | INFO     | task_manager:_process_upload_task:272 - Image saved for test_device_legacy_123: /private/share/chat/19c42472576ba631a6d1d859c2f1b867.png
2025-09-02 13:43:26 | DEBUG    | services:send_message:122 - Kafka message sent: {"frame_time": 1756791806.6, "longitude": 0, "latitude": 0, "degree": 0, "ts": 0, "device_id": "test...
2025-09-02 13:43:26 | INFO     | task_manager:_send_processing_results:363 - Sent image to image_caption: /private/share/chat/19c42472576ba631a6d1d859c2f1b867.png
2025-09-02 13:43:26 | DEBUG    | services:send_message:122 - Kafka message sent: {"frame_time": 1756791806.6, "longitude": 0, "latitude": 0, "degree": 0, "ts": 0, "device_id": "test...
2025-09-02 13:43:26 | INFO     | task_manager:_send_processing_results:390 - Sent trigger message for combined processing
2025-09-02 13:43:26 | INFO     | task_manager:_process_task:214 - Task test_device_legacy_123 completed successfully
2025-09-02 13:43:27 | DEBUG    | message_handler:_process_kafka_message:226 - Received Kafka message: {'frame_time': 1756791806.6, 'longitude': 0, 'latitude': 0, 'degree': 0, 'ts': 0, 'device_id': 'test_device_legacy_123', 'exhibition_id': 53, 'user_id': 'user0', 'event_id': 'test_device_legacy_123', 'context': '', 'device_type': 'phone', 'from': 'client', 'frame': '/private/share/chat/19c42472576ba631a6d1d859c2f1b867.png', 'query': '', 'to': 'trigger', 'interface': 'trigger'}
2025-09-02 13:43:27 | DEBUG    | message_handler:_process_kafka_message:252 - Ignoring Kafka message with 'to': trigger
2025-09-02 13:43:27 | DEBUG    | message_handler:_process_kafka_message:226 - Received Kafka message: {'frame_time': 1756791806.6, 'longitude': 0, 'latitude': 0, 'degree': 0, 'ts': 0, 'device_id': 'test_device_legacy_123', 'exhibition_id': 53, 'user_id': 'user0', 'event_id': 'test_device_legacy_123', 'context': '', 'device_type': 'phone', 'from': 'client', 'frame': '/private/share/chat/19c42472576ba631a6d1d859c2f1b867.png', 'to': 'image_caption', 'interface': 'image_caption'}
2025-09-02 13:43:27 | DEBUG    | message_handler:_process_kafka_message:252 - Ignoring Kafka message with 'to': image_caption
2025-09-02 13:43:36 | INFO     | message_handler:remove_client:67 - Client disconnected: test_device_legacy_123
2025-09-02 13:53:25 | DEBUG    | app:_cleanup_loop:336 - Cleanup cycle completed
2025-09-02 13:59:09 | INFO     | __main__:signal_handler:81 - Received SIGINT signal, initiating graceful shutdown...
2025-09-02 13:59:09 | INFO     | __main__:_shutdown:187 - Starting graceful shutdown...
2025-09-02 13:59:09 | INFO     | __main__:_shutdown:191 - Stopping WSGI server...
2025-09-02 13:59:09 | INFO     | __main__:_shutdown:197 - Shutting down application...
2025-09-02 13:59:09 | INFO     | app:shutdown:115 - Shutting down chat application...
2025-09-02 13:59:12 | INFO     | __main__:_setup_logging:71 - Logging configured successfully
2025-09-02 13:59:12 | INFO     | __main__:run:225 - Starting chat application...
2025-09-02 13:59:12 | INFO     | __main__:run:226 - Configuration: Kafka=['127.0.0.1:5602'], Redis=localhost:5601
2025-09-02 13:59:12 | INFO     | __main__:_validate_environment:120 - FFmpeg is available
2025-09-02 13:59:12 | INFO     | __main__:_validate_environment:133 - Environment validation passed
2025-09-02 13:59:12 | INFO     | __main__:_create_application:143 - Creating chat application...
2025-09-02 13:59:12 | INFO     | services:initialize:47 - Redis connected to localhost:5601
2025-09-02 13:59:12 | INFO     | services:initialize:79 - Kafka producer initialized for brokers: ['127.0.0.1:5602']
2025-09-02 13:59:12 | INFO     | services:initialize:192 - External API service initialized
2025-09-02 13:59:12 | INFO     | services:initialize_all:391 - All services initialized successfully
2025-09-02 13:59:12 | INFO     | app:initialize:88 - Chat application initialized successfully
2025-09-02 13:59:12 | INFO     | message_handler:start_kafka_consumer:184 - Kafka consumer started
2025-09-02 13:59:12 | INFO     | task_manager:start_workers:88 - Started 3 task workers
2025-09-02 13:59:12 | INFO     | app:start_background_tasks:107 - Background tasks started
2025-09-02 13:59:12 | INFO     | __main__:_create_application:149 - Chat application created successfully
2025-09-02 13:59:12 | INFO     | __main__:_start_server:159 - Starting server on 0.0.0.0:9214
2025-09-02 13:59:12 | INFO     | __main__:_start_server:168 - Server started successfully on http://0.0.0.0:9214
2025-09-02 13:59:12 | INFO     | __main__:_start_server:169 - Application is ready to accept connections
2025-09-02 13:59:12 | INFO     | task_manager:_worker_loop:163 - Task worker worker-0 started
2025-09-02 13:59:12 | INFO     | task_manager:_worker_loop:163 - Task worker worker-1 started
2025-09-02 13:59:12 | INFO     | task_manager:_worker_loop:163 - Task worker worker-2 started
2025-09-02 13:59:12 | DEBUG    | app:_cleanup_loop:336 - Cleanup cycle completed
2025-09-02 13:59:12 | INFO     | message_handler:_consume_messages:197 - Kafka consumer initialized successfully
2025-09-02 13:59:15 | DEBUG    | app:_handle_websocket:292 - Received WebSocket message from None: query
2025-09-02 13:59:15 | INFO     | message_handler:add_client:41 - New client connected: test_device_legacy_123
2025-09-02 13:59:15 | DEBUG    | message_handler:handle_websocket_message:406 - Received WebSocket message from test_device_legacy_123: query
2025-09-02 13:59:15 | INFO     | message_handler:handle_websocket_message:409 - Query message received from test_device_legacy_123
2025-09-02 13:59:15 | DEBUG    | app:_handle_websocket:308 - Processed WebSocket message from test_device_legacy_123: query
2025-09-02 13:59:15 | INFO     | app:_handle_upload:166 - Received upload request from test_device_legacy_123
2025-09-02 13:59:15 | INFO     | task_manager:submit_upload_task:125 - Submitted upload task: test_device_legacy_123
2025-09-02 13:59:15 | INFO     | app:_handle_upload:197 - Upload task submitted for device test_device_legacy_123
2025-09-02 13:59:15 | INFO     | task_manager:_process_task:190 - Worker worker-0 processing task test_device_legacy_123
2025-09-02 13:59:15 | INFO     | task_manager:_process_task:199 - Processing upload task test_device_legacy_123
2025-09-02 13:59:15 | INFO     | task_manager:_store_user_context:303 - Setting exhibition info for test_device_legacy_123: 53, 人民网综合展厅
2025-09-02 13:59:15 | DEBUG    | audio_processor:convert_to_16khz_mono:61 - Audio conversion successful: /private/share/chat/test_device_legacy_123_16000.wav
2025-09-02 13:59:15 | DEBUG    | utils:__exit__:269 - FFmpeg conversion: test_device_legacy_123.wav completed in 0.048s
2025-09-02 13:59:15 | DEBUG    | utils:__exit__:269 - ASR request for 2078 bytes completed in 0.004s
2025-09-02 13:59:15 | DEBUG    | utils:safe_remove_file:90 - Removed file: /private/share/chat/test_device_legacy_123.wav
2025-09-02 13:59:15 | DEBUG    | utils:safe_remove_file:90 - Removed file: /private/share/chat/test_device_legacy_123_16000.wav
2025-09-02 13:59:15 | WARNING  | audio_processor:process_base64_audio:196 - ASR returned no text
2025-09-02 13:59:15 | INFO     | image_processor:process_base64_image:91 - Image saved for device test_device_legacy_123: /private/share/chat/9b77d31b39260548e05780d9780d1648.png (84 bytes)
2025-09-02 13:59:15 | INFO     | task_manager:_process_upload_task:272 - Image saved for test_device_legacy_123: /private/share/chat/9b77d31b39260548e05780d9780d1648.png
2025-09-02 13:59:15 | DEBUG    | services:send_message:122 - Kafka message sent: {"frame_time": 1756792755.18, "longitude": 0, "latitude": 0, "degree": 0, "ts": 0, "device_id": "tes...
2025-09-02 13:59:15 | INFO     | task_manager:_send_processing_results:363 - Sent image to image_caption: /private/share/chat/9b77d31b39260548e05780d9780d1648.png
2025-09-02 13:59:15 | DEBUG    | services:send_message:122 - Kafka message sent: {"frame_time": 1756792755.18, "longitude": 0, "latitude": 0, "degree": 0, "ts": 0, "device_id": "tes...
2025-09-02 13:59:15 | INFO     | task_manager:_send_processing_results:390 - Sent trigger message for combined processing
2025-09-02 13:59:15 | INFO     | task_manager:_process_task:214 - Task test_device_legacy_123 completed successfully
2025-09-02 13:59:17 | DEBUG    | message_handler:_process_kafka_message:226 - Received Kafka message: {'frame_time': 1756792755.18, 'longitude': 0, 'latitude': 0, 'degree': 0, 'ts': 0, 'device_id': 'test_device_legacy_123', 'exhibition_id': 53, 'user_id': 'user0', 'event_id': 'test_device_legacy_123', 'context': '', 'device_type': 'phone', 'from': 'client', 'frame': '/private/share/chat/9b77d31b39260548e05780d9780d1648.png', 'query': '', 'to': 'trigger', 'interface': 'trigger'}
2025-09-02 13:59:17 | DEBUG    | message_handler:_process_kafka_message:252 - Ignoring Kafka message with 'to': trigger
2025-09-02 13:59:17 | DEBUG    | message_handler:_process_kafka_message:226 - Received Kafka message: {'frame_time': 1756792755.18, 'longitude': 0, 'latitude': 0, 'degree': 0, 'ts': 0, 'device_id': 'test_device_legacy_123', 'exhibition_id': 53, 'user_id': 'user0', 'event_id': 'test_device_legacy_123', 'context': '', 'device_type': 'phone', 'from': 'client', 'frame': '/private/share/chat/9b77d31b39260548e05780d9780d1648.png', 'to': 'image_caption', 'interface': 'image_caption'}
2025-09-02 13:59:17 | DEBUG    | message_handler:_process_kafka_message:252 - Ignoring Kafka message with 'to': image_caption
2025-09-02 13:59:25 | INFO     | message_handler:remove_client:67 - Client disconnected: test_device_legacy_123
2025-09-02 14:01:09 | DEBUG    | app:_handle_websocket:292 - Received WebSocket message from None: query
2025-09-02 14:01:09 | INFO     | message_handler:add_client:41 - New client connected: test_device_legacy_123
2025-09-02 14:01:09 | DEBUG    | message_handler:handle_websocket_message:406 - Received WebSocket message from test_device_legacy_123: query
2025-09-02 14:01:09 | INFO     | message_handler:handle_websocket_message:409 - Query message received from test_device_legacy_123
2025-09-02 14:01:09 | DEBUG    | app:_handle_websocket:308 - Processed WebSocket message from test_device_legacy_123: query
2025-09-02 14:01:09 | INFO     | app:_handle_upload:166 - Received upload request from test_device_legacy_123
2025-09-02 14:01:09 | INFO     | task_manager:submit_upload_task:125 - Submitted upload task: test_device_legacy_123
2025-09-02 14:01:09 | INFO     | app:_handle_upload:197 - Upload task submitted for device test_device_legacy_123
2025-09-02 14:01:09 | INFO     | task_manager:_process_task:190 - Worker worker-0 processing task test_device_legacy_123
2025-09-02 14:01:09 | INFO     | task_manager:_process_task:199 - Processing upload task test_device_legacy_123
2025-09-02 14:01:09 | INFO     | task_manager:_store_user_context:303 - Setting exhibition info for test_device_legacy_123: 53, 人民网综合展厅
2025-09-02 14:01:09 | DEBUG    | audio_processor:convert_to_16khz_mono:61 - Audio conversion successful: /private/share/chat/test_device_legacy_123_16000.wav
2025-09-02 14:01:09 | DEBUG    | utils:__exit__:269 - FFmpeg conversion: test_device_legacy_123.wav completed in 0.048s
2025-09-02 14:01:09 | DEBUG    | utils:__exit__:269 - ASR request for 2078 bytes completed in 0.005s
2025-09-02 14:01:09 | DEBUG    | utils:safe_remove_file:90 - Removed file: /private/share/chat/test_device_legacy_123.wav
2025-09-02 14:01:09 | DEBUG    | utils:safe_remove_file:90 - Removed file: /private/share/chat/test_device_legacy_123_16000.wav
2025-09-02 14:01:09 | WARNING  | audio_processor:process_base64_audio:196 - ASR returned no text
2025-09-02 14:01:09 | INFO     | image_processor:process_base64_image:91 - Image saved for device test_device_legacy_123: /private/share/chat/b658ea91af3b711e7a1c34e3d22f48fa.png (84 bytes)
2025-09-02 14:01:09 | INFO     | task_manager:_process_upload_task:272 - Image saved for test_device_legacy_123: /private/share/chat/b658ea91af3b711e7a1c34e3d22f48fa.png
2025-09-02 14:01:09 | DEBUG    | services:send_message:122 - Kafka message sent: {"frame_time": 1756792869.81, "longitude": 0, "latitude": 0, "degree": 0, "ts": 0, "device_id": "tes...
2025-09-02 14:01:09 | INFO     | task_manager:_send_processing_results:363 - Sent image to image_caption: /private/share/chat/b658ea91af3b711e7a1c34e3d22f48fa.png
2025-09-02 14:01:09 | DEBUG    | services:send_message:122 - Kafka message sent: {"frame_time": 1756792869.81, "longitude": 0, "latitude": 0, "degree": 0, "ts": 0, "device_id": "tes...
2025-09-02 14:01:09 | INFO     | task_manager:_send_processing_results:390 - Sent trigger message for combined processing
2025-09-02 14:01:09 | INFO     | task_manager:_process_task:214 - Task test_device_legacy_123 completed successfully
2025-09-02 14:01:09 | DEBUG    | message_handler:_process_kafka_message:226 - Received Kafka message: {'frame_time': 1756792869.81, 'longitude': 0, 'latitude': 0, 'degree': 0, 'ts': 0, 'device_id': 'test_device_legacy_123', 'exhibition_id': 53, 'user_id': 'user0', 'event_id': 'test_device_legacy_123', 'context': '', 'device_type': 'phone', 'from': 'client', 'frame': '/private/share/chat/b658ea91af3b711e7a1c34e3d22f48fa.png', 'to': 'image_caption', 'interface': 'image_caption'}
2025-09-02 14:01:09 | DEBUG    | message_handler:_process_kafka_message:252 - Ignoring Kafka message with 'to': image_caption
2025-09-02 14:01:09 | DEBUG    | message_handler:_process_kafka_message:226 - Received Kafka message: {'frame_time': 1756792869.81, 'longitude': 0, 'latitude': 0, 'degree': 0, 'ts': 0, 'device_id': 'test_device_legacy_123', 'exhibition_id': 53, 'user_id': 'user0', 'event_id': 'test_device_legacy_123', 'context': '', 'device_type': 'phone', 'from': 'client', 'frame': '/private/share/chat/b658ea91af3b711e7a1c34e3d22f48fa.png', 'query': '', 'to': 'trigger', 'interface': 'trigger'}
2025-09-02 14:01:09 | DEBUG    | message_handler:_process_kafka_message:252 - Ignoring Kafka message with 'to': trigger
2025-09-02 14:01:19 | INFO     | message_handler:remove_client:67 - Client disconnected: test_device_legacy_123
2025-09-02 14:04:36 | DEBUG    | app:_handle_websocket:292 - Received WebSocket message from None: query
2025-09-02 14:04:36 | INFO     | message_handler:add_client:41 - New client connected: test_device_web_123
2025-09-02 14:04:36 | DEBUG    | message_handler:handle_websocket_message:406 - Received WebSocket message from test_device_web_123: query
2025-09-02 14:04:36 | INFO     | message_handler:handle_websocket_message:409 - Query message received from test_device_web_123
2025-09-02 14:04:36 | DEBUG    | app:_handle_websocket:308 - Processed WebSocket message from test_device_web_123: query
2025-09-02 14:09:06 | INFO     | message_handler:remove_client:67 - Client disconnected: test_device_web_123
2025-09-02 14:09:08 | DEBUG    | app:_handle_websocket:292 - Received WebSocket message from None: query
2025-09-02 14:09:08 | INFO     | message_handler:add_client:41 - New client connected: test_device_web_123
2025-09-02 14:09:08 | DEBUG    | message_handler:handle_websocket_message:406 - Received WebSocket message from test_device_web_123: query
2025-09-02 14:09:08 | INFO     | message_handler:handle_websocket_message:409 - Query message received from test_device_web_123
2025-09-02 14:09:08 | DEBUG    | app:_handle_websocket:308 - Processed WebSocket message from test_device_web_123: query
2025-09-02 14:09:13 | DEBUG    | utils:safe_remove_file:90 - Removed file: /private/share/chat/0b1c46bd697c176e69be24e010a0f752.jpg
2025-09-02 14:09:13 | DEBUG    | image_processor:cleanup_old_images:225 - Removed old image file: 0b1c46bd697c176e69be24e010a0f752.jpg
2025-09-02 14:09:13 | DEBUG    | utils:safe_remove_file:90 - Removed file: /private/share/chat/369a4706a944e9fd13f0d750ed54721b.jpg
2025-09-02 14:09:13 | DEBUG    | image_processor:cleanup_old_images:225 - Removed old image file: 369a4706a944e9fd13f0d750ed54721b.jpg
2025-09-02 14:09:13 | DEBUG    | utils:safe_remove_file:90 - Removed file: /private/share/chat/09e20914a0c8629619056119305f8a69.jpg
2025-09-02 14:09:13 | DEBUG    | image_processor:cleanup_old_images:225 - Removed old image file: 09e20914a0c8629619056119305f8a69.jpg
2025-09-02 14:09:13 | DEBUG    | utils:safe_remove_file:90 - Removed file: /private/share/chat/9b0e1bafc2b8ed5f923228a834f25acf.jpg
2025-09-02 14:09:13 | DEBUG    | image_processor:cleanup_old_images:225 - Removed old image file: 9b0e1bafc2b8ed5f923228a834f25acf.jpg
2025-09-02 14:09:13 | DEBUG    | utils:safe_remove_file:90 - Removed file: /private/share/chat/277c8959db821ee20a5ee0adfcf40a29.jpg
2025-09-02 14:09:13 | DEBUG    | image_processor:cleanup_old_images:225 - Removed old image file: 277c8959db821ee20a5ee0adfcf40a29.jpg
2025-09-02 14:09:13 | DEBUG    | utils:safe_remove_file:90 - Removed file: /private/share/chat/5f5d4a7c6ab123e4e085ce9b0efa86c4.jpg
2025-09-02 14:09:13 | DEBUG    | image_processor:cleanup_old_images:225 - Removed old image file: 5f5d4a7c6ab123e4e085ce9b0efa86c4.jpg
2025-09-02 14:09:13 | DEBUG    | utils:safe_remove_file:90 - Removed file: /private/share/chat/ceb14d8454b38e44d4602739f4b2efe3.jpg
2025-09-02 14:09:13 | DEBUG    | image_processor:cleanup_old_images:225 - Removed old image file: ceb14d8454b38e44d4602739f4b2efe3.jpg
2025-09-02 14:09:13 | DEBUG    | utils:safe_remove_file:90 - Removed file: /private/share/chat/2fb6d4e42c93e20088b9030bb17f44ec.jpg
2025-09-02 14:09:13 | DEBUG    | image_processor:cleanup_old_images:225 - Removed old image file: 2fb6d4e42c93e20088b9030bb17f44ec.jpg
2025-09-02 14:09:13 | DEBUG    | utils:safe_remove_file:90 - Removed file: /private/share/chat/8e07f19ed86995005b764073374867a8.jpg
2025-09-02 14:09:13 | DEBUG    | image_processor:cleanup_old_images:225 - Removed old image file: 8e07f19ed86995005b764073374867a8.jpg
2025-09-02 14:09:13 | INFO     | image_processor:cleanup_old_images:228 - Cleaned up 9 old image files
2025-09-02 14:09:13 | DEBUG    | app:_cleanup_loop:336 - Cleanup cycle completed
2025-09-02 14:10:05 | DEBUG    | app:_handle_websocket:292 - Received WebSocket message from test_device_web_123: query
2025-09-02 14:10:05 | INFO     | message_handler:add_client:53 - Client reconnected: test_device_web_123
2025-09-02 14:10:05 | DEBUG    | message_handler:handle_websocket_message:406 - Received WebSocket message from test_device_web_123: query
2025-09-02 14:10:05 | INFO     | message_handler:handle_websocket_message:409 - Query message received from test_device_web_123
2025-09-02 14:10:05 | DEBUG    | app:_handle_websocket:308 - Processed WebSocket message from test_device_web_123: query
2025-09-02 14:10:17 | DEBUG    | app:_handle_websocket:292 - Received WebSocket message from test_device_web_123: play_end
2025-09-02 14:10:17 | INFO     | message_handler:add_client:53 - Client reconnected: test_device_web_123
2025-09-02 14:10:17 | DEBUG    | message_handler:handle_websocket_message:406 - Received WebSocket message from test_device_web_123: play_end
2025-09-02 14:10:17 | DEBUG    | message_handler:handle_websocket_message:413 - Play end message received from test_device_web_123
2025-09-02 14:10:17 | DEBUG    | app:_handle_websocket:308 - Processed WebSocket message from test_device_web_123: play_end
2025-09-02 14:10:17 | DEBUG    | message_handler:_send_next_message:349 - No messages to send for client test_device_web_123
2025-09-02 14:10:36 | INFO     | message_handler:remove_client:67 - Client disconnected: test_device_web_123
2025-09-02 14:12:09 | DEBUG    | app:_handle_websocket:292 - Received WebSocket message from None: query
2025-09-02 14:12:09 | INFO     | message_handler:add_client:41 - New client connected: test_device_legacy_123
2025-09-02 14:12:09 | DEBUG    | message_handler:handle_websocket_message:406 - Received WebSocket message from test_device_legacy_123: query
2025-09-02 14:12:09 | INFO     | message_handler:handle_websocket_message:409 - Query message received from test_device_legacy_123
2025-09-02 14:12:09 | DEBUG    | app:_handle_websocket:308 - Processed WebSocket message from test_device_legacy_123: query
2025-09-02 14:12:09 | INFO     | app:_handle_upload:166 - Received upload request from test_device_legacy_123
2025-09-02 14:12:09 | INFO     | task_manager:submit_upload_task:125 - Submitted upload task: test_device_legacy_123
2025-09-02 14:12:09 | INFO     | app:_handle_upload:197 - Upload task submitted for device test_device_legacy_123
2025-09-02 14:12:09 | INFO     | task_manager:_process_task:190 - Worker worker-0 processing task test_device_legacy_123
2025-09-02 14:12:09 | INFO     | task_manager:_process_task:199 - Processing upload task test_device_legacy_123
2025-09-02 14:12:09 | INFO     | task_manager:_store_user_context:303 - Setting exhibition info for test_device_legacy_123: 53, 人民网综合展厅
2025-09-02 14:12:09 | DEBUG    | audio_processor:convert_to_16khz_mono:61 - Audio conversion successful: /private/share/chat/test_device_legacy_123_16000.wav
2025-09-02 14:12:09 | DEBUG    | utils:__exit__:269 - FFmpeg conversion: test_device_legacy_123.wav completed in 0.048s
2025-09-02 14:12:09 | DEBUG    | utils:__exit__:269 - ASR request for 2078 bytes completed in 0.005s
2025-09-02 14:12:09 | DEBUG    | utils:safe_remove_file:90 - Removed file: /private/share/chat/test_device_legacy_123.wav
2025-09-02 14:12:09 | DEBUG    | utils:safe_remove_file:90 - Removed file: /private/share/chat/test_device_legacy_123_16000.wav
2025-09-02 14:12:09 | WARNING  | audio_processor:process_base64_audio:196 - ASR returned no text
2025-09-02 14:12:09 | INFO     | image_processor:process_base64_image:91 - Image saved for device test_device_legacy_123: /private/share/chat/60694fa306cede019c058648e0738615.png (84 bytes)
2025-09-02 14:12:09 | INFO     | task_manager:_process_upload_task:272 - Image saved for test_device_legacy_123: /private/share/chat/60694fa306cede019c058648e0738615.png
2025-09-02 14:12:09 | DEBUG    | services:send_message:122 - Kafka message sent: {"frame_time": 1756793529.56, "longitude": 0, "latitude": 0, "degree": 0, "ts": 0, "device_id": "tes...
2025-09-02 14:12:09 | INFO     | task_manager:_send_processing_results:363 - Sent image to image_caption: /private/share/chat/60694fa306cede019c058648e0738615.png
2025-09-02 14:12:09 | DEBUG    | services:send_message:122 - Kafka message sent: {"frame_time": 1756793529.56, "longitude": 0, "latitude": 0, "degree": 0, "ts": 0, "device_id": "tes...
2025-09-02 14:12:09 | INFO     | task_manager:_send_processing_results:390 - Sent trigger message for combined processing
2025-09-02 14:12:09 | INFO     | task_manager:_process_task:214 - Task test_device_legacy_123 completed successfully
2025-09-02 14:12:09 | DEBUG    | message_handler:_process_kafka_message:226 - Received Kafka message: {'frame_time': 1756793529.56, 'longitude': 0, 'latitude': 0, 'degree': 0, 'ts': 0, 'device_id': 'test_device_legacy_123', 'exhibition_id': 53, 'user_id': 'user0', 'event_id': 'test_device_legacy_123', 'context': '', 'device_type': 'phone', 'from': 'client', 'frame': '/private/share/chat/60694fa306cede019c058648e0738615.png', 'query': '', 'to': 'trigger', 'interface': 'trigger'}
2025-09-02 14:12:09 | DEBUG    | message_handler:_process_kafka_message:252 - Ignoring Kafka message with 'to': trigger
2025-09-02 14:12:09 | DEBUG    | message_handler:_process_kafka_message:226 - Received Kafka message: {'frame_time': 1756793529.56, 'longitude': 0, 'latitude': 0, 'degree': 0, 'ts': 0, 'device_id': 'test_device_legacy_123', 'exhibition_id': 53, 'user_id': 'user0', 'event_id': 'test_device_legacy_123', 'context': '', 'device_type': 'phone', 'from': 'client', 'frame': '/private/share/chat/60694fa306cede019c058648e0738615.png', 'to': 'image_caption', 'interface': 'image_caption'}
2025-09-02 14:12:09 | DEBUG    | message_handler:_process_kafka_message:252 - Ignoring Kafka message with 'to': image_caption
2025-09-02 14:12:19 | INFO     | message_handler:remove_client:67 - Client disconnected: test_device_legacy_123
2025-09-02 14:14:07 | INFO     | __main__:signal_handler:81 - Received SIGINT signal, initiating graceful shutdown...
2025-09-02 14:14:07 | INFO     | __main__:_shutdown:187 - Starting graceful shutdown...
2025-09-02 14:14:07 | INFO     | __main__:_shutdown:191 - Stopping WSGI server...
2025-09-02 14:14:07 | INFO     | __main__:_shutdown:197 - Shutting down application...
2025-09-02 14:14:07 | INFO     | app:shutdown:115 - Shutting down chat application...
