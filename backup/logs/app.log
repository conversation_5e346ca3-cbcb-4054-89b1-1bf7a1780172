2025-09-01 17:43:49 | INFO     | __main__:_setup_logging:71 - Logging configured successfully
2025-09-01 17:43:49 | INFO     | __main__:run:225 - Starting chat application...
2025-09-01 17:43:49 | INFO     | __main__:run:226 - Configuration: Kafka=['127.0.0.1:5602'], Redis=localhost:5601
2025-09-01 17:43:49 | INFO     | __main__:_validate_environment:110 - Created directory: static/tmp
2025-09-01 17:43:49 | INFO     | __main__:_validate_environment:120 - FFmpeg is available
2025-09-01 17:43:49 | INFO     | __main__:_validate_environment:133 - Environment validation passed
2025-09-01 17:43:49 | INFO     | __main__:_create_application:143 - Creating chat application...
2025-09-01 17:43:49 | INFO     | services:initialize:47 - Redis connected to localhost:5601
2025-09-01 17:43:49 | INFO     | services:initialize:79 - <PERSON><PERSON><PERSON> producer initialized for brokers: ['127.0.0.1:5602']
2025-09-01 17:43:49 | INFO     | services:initialize:192 - External API service initialized
2025-09-01 17:43:49 | INFO     | services:initialize_all:391 - All services initialized successfully
2025-09-01 17:43:49 | INFO     | app:initialize:88 - Chat application initialized successfully
2025-09-01 17:43:49 | INFO     | message_handler:start_kafka_consumer:184 - Kafka consumer started
2025-09-01 17:43:49 | INFO     | task_manager:start_workers:88 - Started 3 task workers
2025-09-01 17:43:49 | INFO     | app:start_background_tasks:107 - Background tasks started
2025-09-01 17:43:49 | INFO     | __main__:_create_application:149 - Chat application created successfully
2025-09-01 17:43:49 | INFO     | __main__:_start_server:159 - Starting server on 0.0.0.0:9214
2025-09-01 17:43:49 | INFO     | __main__:_start_server:168 - Server started successfully on http://0.0.0.0:9214
2025-09-01 17:43:49 | INFO     | __main__:_start_server:169 - Application is ready to accept connections
2025-09-01 17:43:49 | INFO     | task_manager:_worker_loop:163 - Task worker worker-0 started
2025-09-01 17:43:49 | INFO     | task_manager:_worker_loop:163 - Task worker worker-1 started
2025-09-01 17:43:49 | INFO     | task_manager:_worker_loop:163 - Task worker worker-2 started
2025-09-01 17:43:49 | DEBUG    | app:_cleanup_loop:335 - Cleanup cycle completed
2025-09-01 17:43:49 | INFO     | message_handler:_consume_messages:197 - Kafka consumer initialized successfully
2025-09-01 17:53:50 | DEBUG    | app:_cleanup_loop:335 - Cleanup cycle completed
2025-09-01 18:03:50 | DEBUG    | app:_cleanup_loop:335 - Cleanup cycle completed
2025-09-01 18:06:13 | INFO     | __main__:signal_handler:81 - Received SIGINT signal, initiating graceful shutdown...
2025-09-01 18:06:13 | INFO     | __main__:_shutdown:187 - Starting graceful shutdown...
2025-09-01 18:06:13 | INFO     | __main__:_shutdown:191 - Stopping WSGI server...
2025-09-01 18:06:13 | INFO     | __main__:_shutdown:197 - Shutting down application...
2025-09-01 18:06:13 | INFO     | app:shutdown:115 - Shutting down chat application...
