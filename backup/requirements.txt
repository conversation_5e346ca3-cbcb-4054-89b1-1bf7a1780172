# # Core web framework and async support
# Flask==2.3.3
# Flask-Sockets==0.2.1
# Flask-CORS==4.0.0
# gevent==23.7.0
# gevent-websocket==0.10.1

# # Message queue and caching
# kafka-python==2.0.2
# redis==4.6.0

# # HTTP requests
# requests==2.31.0

# # Logging
# loguru==0.7.0

# # Process management
# psutil==5.9.5

# # Data processing
# Pillow==10.0.0  # For image processing validation (optional)

# # Development and testing (optional)
# pytest==7.4.0
# pytest-cov==4.1.0
# black==23.7.0
# flake8==6.0.0
bidict==0.23.1
certifi==2024.2.2
charset-normalizer==3.3.2
click==7.1.2
Flask==1.1.4
Flask-Cors==4.0.1
Flask-SocketIO==5.5.1
Flask-Sockets==0.2.1
gevent==24.2.1
gevent-websocket==0.10.1
greenlet==3.0.3
h11==0.14.0
idna==3.7
itsdangerous==1.1.0
Jinja2==2.11.3
kafka-python==2.0.2
loguru==0.7.2
MarkupSafe==1.1.1
python-engineio==4.11.2
python-socketio==5.12.1
requests==2.31.0
simple-websocket==1.1.0
urllib3==2.2.1
# Werkzeug==1.0.1
# wsproto==1.2.0
# zope.event==5.0
# zope.interface==6.4.post2
