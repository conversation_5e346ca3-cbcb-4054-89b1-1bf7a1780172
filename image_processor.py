"""
Image processing module for handling image upload, storage, and processing.
Manages image files with proper validation and cleanup.
"""

import os
import time
from typing import Optional, Dict, List, Tuple
from base64 import b64decode
from loguru import logger

from config import Config
from utils import get_file_name, safe_remove_file, ensure_directory_exists, get_file_age_seconds


class ImageProcessor:
    """Handles image processing operations including upload, storage, and cleanup."""
    
    def __init__(self, config: Config):
        self.config = config
        self.base_share_dir = config.file.base_share_dir
        self.supported_formats = {'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'}
        self.max_file_size = 10 * 1024 * 1024  # 10MB default
        
        # Ensure directory exists
        ensure_directory_exists(self.base_share_dir)
    
    def process_base64_image(self, image_b64: str, device_id: str, 
                           format_hint: str = 'jpg') -> Optional[str]:
        """
        Process base64 encoded image data and save to file.
        
        Args:
            image_b64: Base64 encoded image data (with or without data URL prefix)
            device_id: Device identifier for logging
            format_hint: Suggested file format
            
        Returns:
            Full path to saved image file, or None on error
        """
        if not image_b64:
            logger.warning("Empty image data provided")
            return None
        
        try:
            # Remove data URL prefix if present
            if ',' in image_b64:
                # Extract format from data URL if available
                prefix = image_b64[:image_b64.find(',')]
                if 'image/' in prefix:
                    format_from_url = self._extract_format_from_data_url(prefix)
                    if format_from_url:
                        format_hint = format_from_url
                
                image_data = image_b64[image_b64.find(',') + 1:]
            else:
                image_data = image_b64
            
            # Decode base64 image data
            try:
                decoded_image = b64decode(image_data)
            except Exception as e:
                logger.error(f"Failed to decode base64 image for device {device_id}: {e}")
                return None
            
            # Validate image size
            if len(decoded_image) > self.max_file_size:
                logger.error(f"Image too large for device {device_id}: {len(decoded_image)} bytes")
                return None
            
            if len(decoded_image) == 0:
                logger.error(f"Empty image data for device {device_id}")
                return None
            
            # Validate format
            actual_format = self._detect_image_format(decoded_image)
            if actual_format:
                format_hint = actual_format
            
            if format_hint.lower() not in self.supported_formats:
                logger.warning(f"Unsupported image format '{format_hint}' for device {device_id}, using jpg")
                format_hint = 'jpg'
            
            # Generate unique filename and save
            filename = get_file_name(format_hint.lower())
            file_path = os.path.join(self.base_share_dir, filename)
            
            with open(file_path, "wb") as f:
                f.write(decoded_image)
            
            logger.info(f"Image saved for device {device_id}: {file_path} ({len(decoded_image)} bytes)")
            return file_path
            
        except Exception as e:
            logger.error(f"Image processing failed for device {device_id}: {e}")
            return None
    
    def _extract_format_from_data_url(self, data_url_prefix: str) -> Optional[str]:
        """
        Extract image format from data URL prefix.
        
        Args:
            data_url_prefix: Data URL prefix (e.g., "data:image/jpeg;base64")
            
        Returns:
            Image format string, or None if not found
        """
        try:
            if 'image/' in data_url_prefix:
                start = data_url_prefix.find('image/') + 6
                end = data_url_prefix.find(';', start)
                if end == -1:
                    end = len(data_url_prefix)
                
                format_str = data_url_prefix[start:end].lower()
                
                # Map common MIME types to file extensions
                format_mapping = {
                    'jpeg': 'jpg',
                    'png': 'png',
                    'gif': 'gif',
                    'bmp': 'bmp',
                    'webp': 'webp'
                }
                
                return format_mapping.get(format_str, format_str)
        except Exception:
            pass
        
        return None
    
    def _detect_image_format(self, image_data: bytes) -> Optional[str]:
        """
        Detect image format from binary data using magic bytes.
        
        Args:
            image_data: Binary image data
            
        Returns:
            Detected format string, or None if unknown
        """
        if len(image_data) < 8:
            return None
        
        # Check magic bytes for common image formats
        if image_data.startswith(b'\xFF\xD8\xFF'):
            return 'jpg'
        elif image_data.startswith(b'\x89PNG\r\n\x1a\n'):
            return 'png'
        elif image_data.startswith(b'GIF87a') or image_data.startswith(b'GIF89a'):
            return 'gif'
        elif image_data.startswith(b'BM'):
            return 'bmp'
        elif image_data.startswith(b'RIFF') and b'WEBP' in image_data[:12]:
            return 'webp'
        
        return None
    
    def validate_image_file(self, file_path: str) -> bool:
        """
        Validate that an image file exists and is readable.
        
        Args:
            file_path: Path to image file
            
        Returns:
            True if file is valid, False otherwise
        """
        try:
            if not os.path.exists(file_path):
                return False
            
            if not os.path.isfile(file_path):
                return False
            
            file_size = os.path.getsize(file_path)
            if file_size == 0:
                return False
            
            if file_size > self.max_file_size:
                return False
            
            # Try to read first few bytes to ensure file is accessible
            with open(file_path, 'rb') as f:
                header = f.read(16)
                if len(header) == 0:
                    return False
            
            return True
            
        except Exception as e:
            logger.warning(f"Image file validation failed for {file_path}: {e}")
            return False
    
    def cleanup_old_images(self, max_age_seconds: int = 24 * 60 * 60) -> int:
        """
        Clean up old image files from the share directory.
        
        Args:
            max_age_seconds: Maximum age in seconds (default: 24 hours)
            
        Returns:
            Number of files removed
        """
        removed_count = 0
        
        try:
            if not os.path.exists(self.base_share_dir):
                return 0
            
            for filename in os.listdir(self.base_share_dir):
                file_path = os.path.join(self.base_share_dir, filename)
                
                if not os.path.isfile(file_path):
                    continue
                
                # Only process image files
                if not self._is_image_file(filename):
                    continue
                
                file_age = get_file_age_seconds(file_path)
                if file_age is not None and file_age > max_age_seconds:
                    if safe_remove_file(file_path):
                        removed_count += 1
                        logger.debug(f"Removed old image file: {filename}")
            
            if removed_count > 0:
                logger.info(f"Cleaned up {removed_count} old image files")
            
        except Exception as e:
            logger.error(f"Image cleanup failed: {e}")
        
        return removed_count
    
    def _is_image_file(self, filename: str) -> bool:
        """
        Check if filename appears to be an image file based on extension.
        
        Args:
            filename: Filename to check
            
        Returns:
            True if appears to be an image file
        """
        try:
            extension = filename.lower().split('.')[-1]
            return extension in self.supported_formats
        except Exception:
            return False
    
    def get_image_stats(self) -> Dict:
        """
        Get statistics about image files in the share directory.
        
        Returns:
            Dictionary with image file statistics
        """
        stats = {
            'total_images': 0,
            'total_size_bytes': 0,
            'formats': {},
            'oldest_image_age_seconds': 0,
            'newest_image_age_seconds': 0
        }
        
        try:
            if not os.path.exists(self.base_share_dir):
                return stats
            
            current_time = time.time()
            image_ages = []
            
            for filename in os.listdir(self.base_share_dir):
                file_path = os.path.join(self.base_share_dir, filename)
                
                if not os.path.isfile(file_path) or not self._is_image_file(filename):
                    continue
                
                stats['total_images'] += 1
                
                try:
                    file_size = os.path.getsize(file_path)
                    stats['total_size_bytes'] += file_size
                    
                    file_age = current_time - os.path.getmtime(file_path)
                    image_ages.append(file_age)
                    
                    # Count formats
                    extension = filename.lower().split('.')[-1]
                    stats['formats'][extension] = stats['formats'].get(extension, 0) + 1
                    
                except OSError:
                    continue
            
            if image_ages:
                stats['oldest_image_age_seconds'] = max(image_ages)
                stats['newest_image_age_seconds'] = min(image_ages)
        
        except Exception as e:
            logger.error(f"Failed to get image stats: {e}")
        
        return stats
    
    def list_recent_images(self, max_count: int = 10, max_age_seconds: int = 3600) -> List[Dict]:
        """
        List recent image files with metadata.
        
        Args:
            max_count: Maximum number of images to return
            max_age_seconds: Maximum age in seconds
            
        Returns:
            List of dictionaries with image metadata
        """
        images = []
        
        try:
            if not os.path.exists(self.base_share_dir):
                return images
            
            current_time = time.time()
            
            # Get all image files with metadata
            image_files = []
            for filename in os.listdir(self.base_share_dir):
                file_path = os.path.join(self.base_share_dir, filename)
                
                if not os.path.isfile(file_path) or not self._is_image_file(filename):
                    continue
                
                try:
                    mtime = os.path.getmtime(file_path)
                    file_age = current_time - mtime
                    
                    if file_age <= max_age_seconds:
                        file_size = os.path.getsize(file_path)
                        image_files.append({
                            'filename': filename,
                            'path': file_path,
                            'size_bytes': file_size,
                            'age_seconds': file_age,
                            'modified_time': mtime
                        })
                except OSError:
                    continue
            
            # Sort by modification time (newest first) and limit
            image_files.sort(key=lambda x: x['modified_time'], reverse=True)
            images = image_files[:max_count]
            
        except Exception as e:
            logger.error(f"Failed to list recent images: {e}")
        
        return images
    
    def get_image_info(self, file_path: str) -> Optional[Dict]:
        """
        Get detailed information about an image file.
        
        Args:
            file_path: Path to image file
            
        Returns:
            Dictionary with image information, or None if file not found
        """
        if not self.validate_image_file(file_path):
            return None
        
        try:
            stat = os.stat(file_path)
            filename = os.path.basename(file_path)
            
            # Detect format from file content
            with open(file_path, 'rb') as f:
                header = f.read(16)
                detected_format = self._detect_image_format(header)
            
            return {
                'filename': filename,
                'path': file_path,
                'size_bytes': stat.st_size,
                'created_time': stat.st_ctime,
                'modified_time': stat.st_mtime,
                'detected_format': detected_format,
                'extension': filename.lower().split('.')[-1] if '.' in filename else None
            }
            
        except Exception as e:
            logger.error(f"Failed to get image info for {file_path}: {e}")
            return None
