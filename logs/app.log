2025-09-02 14:34:14.124 | DEBUG    | __main__:<module>:583 - Map([<Rule '/upload' (POST, OPTIONS) -> chat_stream>,
 <Rule '/chat/static/<filename>' (OPTIONS, GET, HEAD) -> static>])
2025-09-02 14:34:14.230 | INFO     | __main__:consume_task:117 - Start consume task success!
2025-09-02 14:34:33.187 | WARNING  | __main__:gen_socket:531 - [Next==>] Received Msg, id is: test_device_legacy_123, msg_type:query
2025-09-02 14:34:33.188 | INFO     | __main__:gen_socket:537 - test_device_legacy_123: New connected
2025-09-02 14:34:33.188 | INFO     | __main__:gen_socket:552 - test_device_legacy_123: Query received, Do nothing! 
2025-09-02 14:34:33.295 | INFO     | __main__:worker:417 - Firstly Get exhibition info: 53, 人民网综合展厅
2025-09-02 14:34:33.353 | INFO     | __main__:worker:448 - ASR done in 0.01s, text=''
2025-09-02 14:34:33.356 | INFO     | __main__:worker:468 -  test_device_legacy_123:   + 图像: /private/share/chat/e26db0c9c400e9da1a78d017e76f4a45.jpg
2025-09-02 14:34:33.356 | INFO     | __main__:worker:484 - Send Image_path to Kafka finished: /private/share/chat/e26db0c9c400e9da1a78d017e76f4a45.jpg
2025-09-02 14:34:33.357 | INFO     | __main__:worker:516 - Send image to image_caption finished: /private/share/chat/e26db0c9c400e9da1a78d017e76f4a45.jpg
2025-09-02 14:34:43.309 | WARNING  | __main__:gen_socket:573 - id: test_device_legacy_123 closed - removing client
