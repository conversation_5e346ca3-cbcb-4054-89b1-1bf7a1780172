#!/usr/bin/env python3
"""
Main entry point for the chat application.
Handles application startup, configuration, and graceful shutdown.
"""

import os
import sys
import signal
import atexit
from gevent import monkey
from loguru import logger
from gevent import pywsgi

# Apply gevent monkey patching before importing other modules
monkey.patch_all()

from config import config
from app import create_app


class ApplicationRunner:
    """Manages the application lifecycle including startup and shutdown."""
    
    def __init__(self):
        self.app = None
        self.server = None
        self.shutdown_requested = False
        
        # Setup logging
        self._setup_logging()
        
        # Register signal handlers for graceful shutdown
        self._setup_signal_handlers()
        
        # Register cleanup function
        atexit.register(self._cleanup)
    
    def _setup_logging(self) -> None:
        """Configure application logging."""
        try:
            # Ensure log directory exists
            os.makedirs(config.app.log_dir, exist_ok=True)
            
            # Remove default logger
            logger.remove()
            
            # Add console logger
            logger.add(
                sys.stderr,
                level="INFO",
                format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                       "<level>{level: <8}</level> | "
                       "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
                       "<level>{message}</level>",
                colorize=True
            )
            
            # Add file logger
            log_file = os.path.join(config.app.log_dir, "app.log")
            logger.add(
                log_file,
                level="DEBUG",
                format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
                rotation=config.app.log_rotation,
                retention=config.app.log_retention,
                compression="gz"
            )
            
            logger.info("Logging configured successfully")
            
        except Exception as e:
            print(f"Failed to setup logging: {e}")
            sys.exit(1)
    
    def _setup_signal_handlers(self) -> None:
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            signal_name = signal.Signals(signum).name
            logger.info(f"Received {signal_name} signal, initiating graceful shutdown...")
            self.shutdown_requested = True

            # Use gevent to schedule shutdown in the main loop
            import gevent
            gevent.spawn(self._shutdown)

        # Register handlers for common termination signals
        signal.signal(signal.SIGINT, signal_handler)   # Ctrl+C
        signal.signal(signal.SIGTERM, signal_handler)  # Termination request

        # On Unix systems, also handle SIGHUP
        if hasattr(signal, 'SIGHUP'):
            signal.signal(signal.SIGHUP, signal_handler)
    
    def _validate_environment(self) -> bool:
        """Validate that the environment is properly configured."""
        try:
            # Check required directories
            required_dirs = [
                config.file.static_tmp_dir,
                config.file.base_share_dir,
                config.app.log_dir
            ]
            
            for directory in required_dirs:
                if not os.path.exists(directory):
                    try:
                        os.makedirs(directory, exist_ok=True)
                        logger.info(f"Created directory: {directory}")
                    except Exception as e:
                        logger.error(f"Failed to create required directory {directory}: {e}")
                        return False
            
            # Check if FFmpeg is available (for audio processing)
            try:
                import subprocess
                subprocess.run(["ffmpeg", "-version"], 
                             capture_output=True, check=True, timeout=5)
                logger.info("FFmpeg is available")
            except Exception:
                logger.warning("FFmpeg not found - audio conversion may not work")
            
            # Validate configuration
            if not config.kafka.brokers:
                logger.error("Kafka brokers not configured")
                return False
            
            if not config.redis.host:
                logger.error("Redis host not configured")
                return False
            
            logger.info("Environment validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Environment validation failed: {e}")
            return False
    
    def _create_application(self) -> bool:
        """Create and initialize the chat application."""
        try:
            logger.info("Creating chat application...")
            self.app = create_app(config)
            
            # Start background tasks
            self.app.start_background_tasks()
            
            logger.info("Chat application created successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create application: {e}")
            return False
    
    def _start_server(self) -> None:
        """Start the WSGI server."""
        try:
            logger.info(f"Starting server on {config.app.host}:{config.app.port}")
            
            self.server = pywsgi.WSGIServer(
                (config.app.host, config.app.port),
                self.app.app,
                log=logger
            )
            
            logger.info(f"Server started successfully on http://{config.app.host}:{config.app.port}")
            logger.info("Application is ready to accept connections")
            
            # Start serving requests
            self.server.serve_forever()
            
        except Exception as e:
            logger.error(f"Server error: {e}")
            self._shutdown()
    
    def _shutdown(self) -> None:
        """Perform graceful shutdown."""
        if hasattr(self, '_shutting_down') and self._shutting_down:
            return  # Already shutting down

        self._shutting_down = True
        self.shutdown_requested = True

        try:
            logger.info("Starting graceful shutdown...")

            # Stop the server first
            if self.server:
                logger.info("Stopping WSGI server...")
                self.server.stop()
                self.server = None

            # Shutdown the application
            if self.app:
                logger.info("Shutting down application...")
                self.app.shutdown()
                self.app = None

            logger.info("Graceful shutdown completed")

            # Force exit if needed
            import sys
            sys.exit(0)

        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
            import sys
            sys.exit(1)
    
    def _cleanup(self) -> None:
        """Cleanup function called on exit."""
        if not self.shutdown_requested:
            self._shutdown()
    
    def run(self) -> int:
        """
        Main application entry point.
        
        Returns:
            Exit code (0 for success, non-zero for error)
        """
        try:
            logger.info("Starting chat application...")
            logger.info(f"Configuration: Kafka={config.kafka.brokers}, Redis={config.redis.host}:{config.redis.port}")
            
            # Validate environment
            if not self._validate_environment():
                logger.error("Environment validation failed")
                return 1
            
            # Create application
            if not self._create_application():
                logger.error("Failed to create application")
                return 1
            
            # Start server
            self._start_server()
            
            return 0
            
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
            return 0
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            return 1
        finally:
            self._shutdown()


def main() -> int:
    """Main function."""
    runner = ApplicationRunner()
    return runner.run()


if __name__ == "__main__":
    sys.exit(main())
