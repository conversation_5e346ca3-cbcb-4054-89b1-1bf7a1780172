"""
Message handling module for Kafka consumption and SSE communication.
Manages SSE sessions, event streaming, and message delivery.
"""

import time
import traceback
from typing import Dict, Any
import gevent
from loguru import logger

from config import Config
from services import ServiceManager
from audio_processor import AudioProcessor
from sse_manager import SSEManager
from utils import safe_json_loads


# class ClientManager:
#     """Manages WebSocket client connections and their message queues."""
    
#     def __init__(self, config: Config):
#         self.config = config
#         self.max_msg_queue = config.app.max_msg_queue_per_client
#         self.clients: Dict[str, Dict] = {}
#         self.clients_lock = RLock()
    
#     def add_client(self, client_id: str, websocket, msg_type: str = None) -> None:
#         """
#         Add or update a client connection.
        
#         Args:
#             client_id: Unique client identifier
#             websocket: WebSocket connection object
#             msg_type: Message type for this client
#         """
#         with self.clients_lock:
#             if client_id not in self.clients:
#                 logger.info(f"New client connected: {client_id}")
#                 self.clients[client_id] = {
#                     "ws": websocket,
#                     "msg_list": deque(),
#                     "msg_type": msg_type,
#                     "first_sent": True,
#                     "connected_time": time.time(),
#                     "waiting_for_play": False,
#                     "last_activity": time.time()
#                 }
#             else:
#                 # Update existing client (reconnection)
#                 logger.info(f"Client reconnected: {client_id}")
#                 self.clients[client_id]["ws"] = websocket
#                 self.clients[client_id]["last_activity"] = time.time()
    
#     def remove_client(self, client_id: str = None, websocket=None) -> None:
#         """
#         Remove a client connection.
        
#         Args:
#             client_id: Client ID to remove (if known)
#             websocket: WebSocket object to find and remove
#         """
#         with self.clients_lock:
#             if client_id and client_id in self.clients:
#                 logger.info(f"Client disconnected: {client_id}")
#                 del self.clients[client_id]
#             elif websocket:
#                 # Find client by websocket object
#                 remove_key = None
#                 for key, client in self.clients.items():
#                     if client.get('ws') == websocket:
#                         remove_key = key
#                         break
                
#                 if remove_key:
#                     logger.info(f"Client disconnected: {remove_key}")
#                     del self.clients[remove_key]
    
#     def get_client(self, client_id: str) -> Optional[Dict]:
#         """Get client information by ID."""
#         with self.clients_lock:
#             return self.clients.get(client_id)
    
#     def add_message_to_queue(self, client_id: str, message: Dict[str, Any]) -> bool:
#         """
#         Add a message to client's queue with overflow protection.
        
#         Args:
#             client_id: Target client ID
#             message: Message to queue
            
#         Returns:
#             True if message was queued successfully
#         """
#         with self.clients_lock:
#             client = self.clients.get(client_id)
#             if not client:
#                 logger.warning(f"Attempted to queue message for unknown client: {client_id}")
#                 return False
            
#             msg_queue = client['msg_list']
            
#             # Prevent queue overflow
#             if len(msg_queue) >= self.max_msg_queue:
#                 dropped_msg = msg_queue.popleft()
#                 logger.warning(f"Dropped oldest message for client {client_id}: {dropped_msg.get('msg_type', 'unknown')}")
            
#             msg_queue.append(message)
#             client['last_activity'] = time.time()
            
#             logger.debug(f"Queued message for client {client_id}: {message.get('msg_type', 'unknown')}")
#             return True
    
#     def get_next_message(self, client_id: str) -> Optional[Dict[str, Any]]:
#         """Get the next message from client's queue without removing it."""
#         with self.clients_lock:
#             client = self.clients.get(client_id)
#             if not client or len(client['msg_list']) == 0:
#                 return None
#             return client['msg_list'][0]
    
#     def remove_sent_message(self, client_id: str) -> bool:
#         """Remove the first message from client's queue after successful send."""
#         with self.clients_lock:
#             client = self.clients.get(client_id)
#             if not client or len(client['msg_list']) == 0:
#                 return False
            
#             try:
#                 client['msg_list'].popleft()
#                 return True
#             except Exception as e:
#                 logger.error(f"Failed to remove sent message for client {client_id}: {e}")
#                 return False
    
#     def get_client_stats(self) -> Dict[str, Any]:
#         """Get statistics about connected clients."""
#         with self.clients_lock:
#             stats = {
#                 'total_clients': len(self.clients),
#                 'clients_with_queued_messages': 0,
#                 'total_queued_messages': 0,
#                 'clients': {}
#             }
            
#             for client_id, client in self.clients.items():
#                 queue_size = len(client['msg_list'])
#                 if queue_size > 0:
#                     stats['clients_with_queued_messages'] += 1
                
#                 stats['total_queued_messages'] += queue_size
#                 stats['clients'][client_id] = {
#                     'queue_size': queue_size,
#                     'connected_time': client.get('connected_time', 0),
#                     'last_activity': client.get('last_activity', 0),
#                     'waiting_for_play': client.get('waiting_for_play', False)
#                 }
            
#             return stats


class MessageHandler:
    """Handles Kafka message consumption and SSE message delivery."""

    def __init__(self, config: Config, service_manager: ServiceManager,
                 audio_processor: AudioProcessor, sse_manager: SSEManager):
        self.config = config
        self.service_manager = service_manager
        self.audio_processor = audio_processor
        self.sse_manager = sse_manager
        self._consumer = None
        self._running = False
    
    def start_kafka_consumer(self) -> None:
        """Start the Kafka consumer in a background greenlet."""
        if self._running:
            logger.warning("Kafka consumer already running")
            return
        
        self._running = True
        gevent.spawn(self._consume_messages)
        logger.info("Kafka consumer started")
    
    def stop_kafka_consumer(self) -> None:
        """Stop the Kafka consumer."""
        self._running = False
        if self._consumer:
            self._consumer.close()
        logger.info("Kafka consumer stopped")
    
    def _consume_messages(self) -> None:
        """Main Kafka consumer loop."""
        try:
            self._consumer = self.service_manager.kafka.create_consumer()
            logger.info("Kafka consumer initialized successfully")
            
            for message in self._consumer:
                if not self._running:
                    break
                
                try:
                    self._process_kafka_message(message)
                except Exception as e:
                    logger.error(f"Error processing Kafka message: {e}\n{traceback.format_exc()}")
                
                # Yield control to other greenlets
                gevent.sleep(0)
                
        except Exception as e:
            logger.error(f"Kafka consumer error: {e}\n{traceback.format_exc()}")
        finally:
            if self._consumer:
                self._consumer.close()
            logger.info("Kafka consumer loop ended")
    
    def _process_kafka_message(self, message) -> None:
        """
        Process a single Kafka message.
        
        Args:
            message: Kafka message object
        """
        try:
            logger.debug(f"Received Kafka message: {message.value}")

            # Handle both raw bytes and pre-decoded data
            if message.value is None:
                logger.warning("Received empty Kafka message")
                return

            if isinstance(message.value, dict):
                # Already decoded by consumer
                data = message.value
            elif isinstance(message.value, (str, bytes)):
                # Need to decode
                if isinstance(message.value, bytes):
                    data = safe_json_loads(message.value.decode())
                else:
                    data = safe_json_loads(message.value)
            else:
                logger.warning(f"Unexpected message value type: {type(message.value)}")
                return

            if not data:
                logger.warning("Received invalid or empty Kafka message data")
                return
            
            # Validate message structure
            if "to" not in data or data['to'] not in ("speak", "stop"):
                logger.debug(f"Ignoring Kafka message with 'to': {data.get('to')}")
                return
            
            device_id = data.get('device_id', "")
            if not device_id:
                logger.warning("Kafka message missing device_id")
                return
            
            logger.debug(f"Processing Kafka message for device {device_id}: {data.get('to')}")

            # Check if device has active SSE sessions
            device_sessions = self.sse_manager.device_sessions.get(device_id, set())
            if not device_sessions:
                logger.debug(f"No active SSE sessions for device {device_id}")
                return
            
            if data['to'] == "stop":
                self._handle_stop_message(device_id, data)
            elif data['to'] == "speak":
                self._handle_speak_message(device_id, data)
                
        except Exception as e:
            logger.error(f"Error processing Kafka message: {e}")
    
    def _handle_stop_message(self, device_id: str, data: Dict[str, Any]) -> None:
        """Handle stop message from Kafka."""
        logger.info(f"Received stop message for device {device_id}")
        # Send stop event to all sessions for this device
        self.sse_manager.send_event_to_device(
            device_id, "stop_message", {
                "msg_type": "stop",
                "meta_data": data.get('meta_data', {}),
                "event_id": data.get('event_id', device_id)
            }
        )
    
    def _handle_speak_message(self, device_id: str, data: Dict[str, Any]) -> None:
        """Handle speak message from Kafka by generating TTS and sending via SSE."""
        sentence = data.get('text', "").strip()
        if not sentence:
            logger.warning(f"Empty text in speak message for device {device_id}")
            return

        is_end = data.get('meta_data', {}).get('is_end', False)
        event_id = data.get('event_id', device_id)

        # Generate TTS audio
        audio_filename = self.audio_processor.text_to_audio_file(sentence)

        if audio_filename:
            # Send audio message via SSE
            audio_message = {
                "msg_type": "wav",
                "wav": f"tmp/{audio_filename}",
                "sent": sentence,
                "meta_data": data.get('meta_data', {}),
                "is_end": is_end,
                "event_id": event_id
            }

            # Send to all sessions for this device
            sent_count = self.sse_manager.send_event_to_device(
                device_id, "audio_message", audio_message, event_id
            )

            if sent_count > 0:
                logger.debug(f"Sent audio message to {sent_count} sessions for device {device_id}")
                # Send Kafka notification for successful delivery
                self._send_kafka_said_event(device_id, audio_message)
            else:
                logger.warning(f"No active sessions to send audio message for device {device_id}")

        # Send end message if this is the final chunk
        if is_end:
            end_message = {
                "msg_type": "end",
                "meta_data": data.get('meta_data', {}),
                "event_id": event_id
            }

            sent_count = self.sse_manager.send_event_to_device(
                device_id, "end_message", end_message, event_id
            )

            if sent_count > 0:
                logger.debug(f"Sent end message to {sent_count} sessions for device {device_id}")
            else:
                logger.warning(f"No active sessions to send end message for device {device_id}")
    
    # Note: _send_next_message method is no longer needed in SSE mode
    # Messages are sent immediately via SSE when received from Kafka
    
    def _send_kafka_said_event(self, device_id: str, message: Dict[str, Any]) -> None:
        """Send a 'said' event back to Kafka after successful message delivery."""
        if message.get('msg_type') == "end":
            return  # Don't send said events for end messages
        
        try:
            said_event = {
                "event_id": message.get('event_id', device_id),
                "device_id": device_id,
                "user_id": "user0",
                "said_text": message.get('sent'),
                "event_flag": "qa",
                "device_type": "phone",
                "is_end": message.get('is_end', True),
                "meta_data": message.get('meta_data', ''),
                "from": "speak",
                "to": "said",
                "interface": "said"
            }
            
            self.service_manager.kafka.send_message(said_event, key=time.time())
            logger.debug(f"Sent 'said' event for device {device_id}")
            
        except Exception as e:
            logger.error(f"Failed to send 'said' event for device {device_id}: {e}")
    
    def handle_client_message(self, device_id: str, message: Dict[str, Any]) -> None:
        """
        Handle incoming message from client (via HTTP API).

        Args:
            device_id: Client device ID
            message: Parsed message data
        """
        msg_type = message.get('msg')
        logger.debug(f"Received client message from {device_id}: {msg_type}")

        if msg_type == "query":
            logger.info(f"Query message received from {device_id}")
            # Query messages don't require special handling in this implementation

        elif msg_type == "play_end":
            logger.debug(f"Play end message received from {device_id}")
            # In SSE mode, client finished playing - no additional action needed
            # The next message will be sent when available from Kafka

        else:
            logger.warning(f"Unknown message type from {device_id}: {msg_type}")
    
    def get_message_stats(self) -> Dict[str, Any]:
        """Get statistics about message handling."""
        sse_stats = self.sse_manager.get_stats()

        return {
            'kafka_consumer_running': self._running,
            'sse_stats': sse_stats
        }
