"""
Message handling module for Kafka consumption and WebSocket communication.
Manages message queues, client connections, and message delivery.
"""

import json
import time
import traceback
from typing import Dict, Any, Optional, Deque
from collections import deque
from threading import RLock
import gevent
from loguru import logger

from config import Config
from services import ServiceManager
from audio_processor import AudioProcessor
from sse_manager import SSEManager
from utils import safe_json_dumps, safe_json_loads


# class ClientManager:
#     """Manages WebSocket client connections and their message queues."""
    
#     def __init__(self, config: Config):
#         self.config = config
#         self.max_msg_queue = config.app.max_msg_queue_per_client
#         self.clients: Dict[str, Dict] = {}
#         self.clients_lock = RLock()
    
#     def add_client(self, client_id: str, websocket, msg_type: str = None) -> None:
#         """
#         Add or update a client connection.
        
#         Args:
#             client_id: Unique client identifier
#             websocket: WebSocket connection object
#             msg_type: Message type for this client
#         """
#         with self.clients_lock:
#             if client_id not in self.clients:
#                 logger.info(f"New client connected: {client_id}")
#                 self.clients[client_id] = {
#                     "ws": websocket,
#                     "msg_list": deque(),
#                     "msg_type": msg_type,
#                     "first_sent": True,
#                     "connected_time": time.time(),
#                     "waiting_for_play": False,
#                     "last_activity": time.time()
#                 }
#             else:
#                 # Update existing client (reconnection)
#                 logger.info(f"Client reconnected: {client_id}")
#                 self.clients[client_id]["ws"] = websocket
#                 self.clients[client_id]["last_activity"] = time.time()
    
#     def remove_client(self, client_id: str = None, websocket=None) -> None:
#         """
#         Remove a client connection.
        
#         Args:
#             client_id: Client ID to remove (if known)
#             websocket: WebSocket object to find and remove
#         """
#         with self.clients_lock:
#             if client_id and client_id in self.clients:
#                 logger.info(f"Client disconnected: {client_id}")
#                 del self.clients[client_id]
#             elif websocket:
#                 # Find client by websocket object
#                 remove_key = None
#                 for key, client in self.clients.items():
#                     if client.get('ws') == websocket:
#                         remove_key = key
#                         break
                
#                 if remove_key:
#                     logger.info(f"Client disconnected: {remove_key}")
#                     del self.clients[remove_key]
    
#     def get_client(self, client_id: str) -> Optional[Dict]:
#         """Get client information by ID."""
#         with self.clients_lock:
#             return self.clients.get(client_id)
    
#     def add_message_to_queue(self, client_id: str, message: Dict[str, Any]) -> bool:
#         """
#         Add a message to client's queue with overflow protection.
        
#         Args:
#             client_id: Target client ID
#             message: Message to queue
            
#         Returns:
#             True if message was queued successfully
#         """
#         with self.clients_lock:
#             client = self.clients.get(client_id)
#             if not client:
#                 logger.warning(f"Attempted to queue message for unknown client: {client_id}")
#                 return False
            
#             msg_queue = client['msg_list']
            
#             # Prevent queue overflow
#             if len(msg_queue) >= self.max_msg_queue:
#                 dropped_msg = msg_queue.popleft()
#                 logger.warning(f"Dropped oldest message for client {client_id}: {dropped_msg.get('msg_type', 'unknown')}")
            
#             msg_queue.append(message)
#             client['last_activity'] = time.time()
            
#             logger.debug(f"Queued message for client {client_id}: {message.get('msg_type', 'unknown')}")
#             return True
    
#     def get_next_message(self, client_id: str) -> Optional[Dict[str, Any]]:
#         """Get the next message from client's queue without removing it."""
#         with self.clients_lock:
#             client = self.clients.get(client_id)
#             if not client or len(client['msg_list']) == 0:
#                 return None
#             return client['msg_list'][0]
    
#     def remove_sent_message(self, client_id: str) -> bool:
#         """Remove the first message from client's queue after successful send."""
#         with self.clients_lock:
#             client = self.clients.get(client_id)
#             if not client or len(client['msg_list']) == 0:
#                 return False
            
#             try:
#                 client['msg_list'].popleft()
#                 return True
#             except Exception as e:
#                 logger.error(f"Failed to remove sent message for client {client_id}: {e}")
#                 return False
    
#     def get_client_stats(self) -> Dict[str, Any]:
#         """Get statistics about connected clients."""
#         with self.clients_lock:
#             stats = {
#                 'total_clients': len(self.clients),
#                 'clients_with_queued_messages': 0,
#                 'total_queued_messages': 0,
#                 'clients': {}
#             }
            
#             for client_id, client in self.clients.items():
#                 queue_size = len(client['msg_list'])
#                 if queue_size > 0:
#                     stats['clients_with_queued_messages'] += 1
                
#                 stats['total_queued_messages'] += queue_size
#                 stats['clients'][client_id] = {
#                     'queue_size': queue_size,
#                     'connected_time': client.get('connected_time', 0),
#                     'last_activity': client.get('last_activity', 0),
#                     'waiting_for_play': client.get('waiting_for_play', False)
#                 }
            
#             return stats


class MessageHandler:
    """Handles Kafka message consumption and WebSocket message delivery."""
    
    def __init__(self, config: Config, service_manager: ServiceManager, 
                 audio_processor: AudioProcessor, sse_manager: SSEManager):
        self.config = config
        self.service_manager = service_manager
        self.audio_processor = audio_processor
        self.client_manager = ClientManager(config)
        self._consumer = None
        self._running = False
    
    def start_kafka_consumer(self) -> None:
        """Start the Kafka consumer in a background greenlet."""
        if self._running:
            logger.warning("Kafka consumer already running")
            return
        
        self._running = True
        gevent.spawn(self._consume_messages)
        logger.info("Kafka consumer started")
    
    def stop_kafka_consumer(self) -> None:
        """Stop the Kafka consumer."""
        self._running = False
        if self._consumer:
            self._consumer.close()
        logger.info("Kafka consumer stopped")
    
    def _consume_messages(self) -> None:
        """Main Kafka consumer loop."""
        try:
            self._consumer = self.service_manager.kafka.create_consumer()
            logger.info("Kafka consumer initialized successfully")
            
            for message in self._consumer:
                if not self._running:
                    break
                
                try:
                    self._process_kafka_message(message)
                except Exception as e:
                    logger.error(f"Error processing Kafka message: {e}\n{traceback.format_exc()}")
                
                # Yield control to other greenlets
                gevent.sleep(0)
                
        except Exception as e:
            logger.error(f"Kafka consumer error: {e}\n{traceback.format_exc()}")
        finally:
            if self._consumer:
                self._consumer.close()
            logger.info("Kafka consumer loop ended")
    
    def _process_kafka_message(self, message) -> None:
        """
        Process a single Kafka message.
        
        Args:
            message: Kafka message object
        """
        try:
            logger.debug(f"Received Kafka message: {message.value}")

            # Handle both raw bytes and pre-decoded data
            if message.value is None:
                logger.warning("Received empty Kafka message")
                return

            if isinstance(message.value, dict):
                # Already decoded by consumer
                data = message.value
            elif isinstance(message.value, (str, bytes)):
                # Need to decode
                if isinstance(message.value, bytes):
                    data = safe_json_loads(message.value.decode())
                else:
                    data = safe_json_loads(message.value)
            else:
                logger.warning(f"Unexpected message value type: {type(message.value)}")
                return

            if not data:
                logger.warning("Received invalid or empty Kafka message data")
                return
            
            # Validate message structure
            if "to" not in data or data['to'] not in ("speak", "stop"):
                logger.debug(f"Ignoring Kafka message with 'to': {data.get('to')}")
                return
            
            device_id = data.get('device_id', "")
            if not device_id:
                logger.warning("Kafka message missing device_id")
                return
            
            logger.debug(f"Processing Kafka message for device {device_id}: {data.get('to')}")
            
            # Check if client is connected
            client = self.client_manager.get_client(device_id)
            if not client:
                logger.debug(f"No connected client for device {device_id}")
                return
            
            if data['to'] == "stop":
                self._handle_stop_message(device_id, data)
            elif data['to'] == "speak":
                self._handle_speak_message(device_id, data)
                
        except Exception as e:
            logger.error(f"Error processing Kafka message: {e}")
    
    def _handle_stop_message(self, device_id: str, data: Dict[str, Any]) -> None:
        """Handle stop message from Kafka."""
        logger.info(f"Received stop message for device {device_id}")
        # Could implement stop logic here if needed
    
    def _handle_speak_message(self, device_id: str, data: Dict[str, Any]) -> None:
        """Handle speak message from Kafka by generating TTS and queueing."""
        sentence = data.get('text', "").strip()
        if not sentence:
            logger.warning(f"Empty text in speak message for device {device_id}")
            return
        
        is_end = data.get('meta_data', {}).get('is_end', False)
        event_id = data.get('event_id', device_id)
        
        # Update first_sent flag
        client = self.client_manager.get_client(device_id)
        if client and client.get('first_sent'):
            client['first_sent'] = False
        
        # Generate TTS audio
        audio_filename = self.audio_processor.text_to_audio_file(sentence)
        
        if audio_filename:
            # Queue audio message
            audio_message = {
                "msg_type": "wav",
                "wav": f"tmp/{audio_filename}",
                "sent": sentence,
                "meta_data": data.get('meta_data', {}),
                "is_end": is_end,
                "event_id": event_id
            }
            
            self.client_manager.add_message_to_queue(device_id, audio_message)
            
            # Trigger immediate send if client is not waiting
            if not client.get('waiting_for_play', False):
                gevent.spawn(self._send_next_message, device_id)
                client['waiting_for_play'] = True
        
        # Queue end message if this is the final chunk
        if is_end:
            end_message = {
                "msg_type": "end",
                "meta_data": data.get('meta_data', {}),
                "event_id": event_id
            }
            self.client_manager.add_message_to_queue(device_id, end_message)
    
    def _send_next_message(self, device_id: str, max_retries: int = 3) -> bool:
        """
        Send the next queued message to a client via WebSocket.
        
        Args:
            device_id: Target client ID
            max_retries: Maximum retry attempts
            
        Returns:
            True if message was sent successfully
        """
        client = self.client_manager.get_client(device_id)
        if not client:
            logger.warning(f"Cannot send message: client {device_id} not found")
            return False
        
        websocket = client.get('ws')
        if not websocket:
            logger.warning(f"Cannot send message: no websocket for client {device_id}")
            return False
        
        message = self.client_manager.get_next_message(device_id)
        if not message:
            logger.debug(f"No messages to send for client {device_id}")
            return False
        
        # Attempt to send with retries
        for attempt in range(max_retries):
            try:
                websocket.send(safe_json_dumps(message))
                logger.debug(f"Message sent to client {device_id} (attempt {attempt + 1}): {message.get('msg_type')}")
                
                # Send successful - remove from queue and send Kafka notification
                self.client_manager.remove_sent_message(device_id)
                self._send_kafka_said_event(device_id, message)
                return True
                
            except Exception as e:
                logger.error(f"WebSocket send error for client {device_id} (attempt {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    gevent.sleep(0.2 * (attempt + 1))  # Exponential backoff
        
        logger.error(f"Failed to send message to client {device_id} after {max_retries} attempts")
        return False
    
    def _send_kafka_said_event(self, device_id: str, message: Dict[str, Any]) -> None:
        """Send a 'said' event back to Kafka after successful message delivery."""
        if message.get('msg_type') == "end":
            return  # Don't send said events for end messages
        
        try:
            said_event = {
                "event_id": message.get('event_id', device_id),
                "device_id": device_id,
                "user_id": "user0",
                "said_text": message.get('sent'),
                "event_flag": "qa",
                "device_type": "phone",
                "is_end": message.get('is_end', True),
                "meta_data": message.get('meta_data', ''),
                "from": "speak",
                "to": "said",
                "interface": "said"
            }
            
            self.service_manager.kafka.send_message(said_event, key=time.time())
            logger.debug(f"Sent 'said' event for device {device_id}")
            
        except Exception as e:
            logger.error(f"Failed to send 'said' event for device {device_id}: {e}")
    
    def handle_websocket_message(self, device_id: str, message: Dict[str, Any]) -> None:
        """
        Handle incoming WebSocket message from client.
        
        Args:
            device_id: Client device ID
            message: Parsed message data
        """
        msg_type = message.get('msg')
        logger.debug(f"Received WebSocket message from {device_id}: {msg_type}")
        
        if msg_type == "query":
            logger.info(f"Query message received from {device_id}")
            # Query messages don't require special handling in this implementation
            
        elif msg_type == "play_end":
            logger.debug(f"Play end message received from {device_id}")
            # Client finished playing current audio - send next message if available
            gevent.spawn(self._send_next_message, device_id)
            
        else:
            logger.warning(f"Unknown message type from {device_id}: {msg_type}")
    
    def get_message_stats(self) -> Dict[str, Any]:
        """Get statistics about message handling."""
        client_stats = self.client_manager.get_client_stats()
        
        return {
            'kafka_consumer_running': self._running,
            'client_stats': client_stats
        }
