# 🚀 新版 SSE 聊天应用 API 文档

## 概述

新版聊天应用采用 **SSE (Server-Sent Events) + HTTP API** 的架构，替代了原来的 WebSocket 双向通信方案。

### 架构特点
- **单向推送**: 服务器通过 SSE 向客户端推送消息
- **HTTP 接收**: 客户端通过 HTTP API 向服务器发送消息
- **简化连接**: 避免了 WebSocket 的连接管理复杂性
- **更好的扩展性**: 支持负载均衡和横向扩展

---

## 🔗 API 端点

### 1. 文件上传接口 (返回 SSE 流)

**`POST /upload`**

上传音频、图片文件并立即返回 SSE 事件流用于接收处理结果。

#### 请求参数
```json
{
  "id": "device_123",           // 必需: 设备ID
  "audio": "data:audio/wav;base64,UklGR...",  // 可选: Base64编码的音频数据
  "video": "data:image/jpeg;base64,/9j/4...", // 可选: Base64编码的图片数据
  "longitude": 116.3974,        // 必需: 经度
  "latitude": 39.9093          // 必需: 纬度
}
```

#### 响应
- **Content-Type**: `text/event-stream`
- **状态码**: 200 (成功) / 400 (参数错误) / 500 (服务器错误)

#### SSE 事件类型

##### `connected` 事件
连接建立成功时发送
```
event: connected
data: {"session_id": "uuid", "device_id": "device_123", "timestamp": **********}
```

##### `audio_message` 事件
返回的音频文本，可以根据"is_end"字段内容判断是否是最后一句，"is_end"为True，表示最后一句
```
event: audio_message
data: {
  "msg_type": "wav",
  "wav": "tmp/audio_file.wav",
  "sent": "处理后的文本内容",
  "meta_data": {...},
  "is_end": false,
  "event_id": "event_123"
}
```

##### `end_message` 事件
所有处理完成时发送
```
event: end_message
data: {
  "msg_type": "end",
  "meta_data": {...},
  "event_id": "event_123"
}
```

##### `heartbeat` 事件
保持连接活跃 (每30秒)
```
event: heartbeat
data: {"timestamp": **********}
```

#### 示例代码

**JavaScript (Fetch API)**
```javascript
const uploadData = {
  id: 'device_123',
  audio: 'data:audio/wav;base64,...',
  video: 'data:image/jpeg;base64,...',
  longitude: 116.3974,
  latitude: 39.9093
};

fetch('/upload', {
  method: 'POST',
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify(uploadData)
})
.then(response => {
  const reader = response.body.getReader();
  const decoder = new TextDecoder();
  
  function readStream() {
    reader.read().then(({done, value}) => {
      if (done) return;
      
      const chunk = decoder.decode(value);
      console.log('SSE Event:', chunk);
      
      readStream();
    });
  }
  
  readStream();
});
```

**Python (requests)**
```python
import requests

upload_data = {
    "id": "device_123",
    "audio": "data:audio/wav;base64,...",
    "video": "data:image/jpeg;base64,...",
    "longitude": 116.3974,
    "latitude": 39.9093
}

response = requests.post(
    'http://localhost:9214/upload',
    json=upload_data,
    stream=True
)

for line in response.iter_lines(decode_unicode=True):
    if line:
        print(f"SSE: {line}")
```

---

### 2. 客户端消息接口

**`POST /message`**

客户端向服务器发送交互消息。

#### 请求参数
```json
{
  "id": "device_123",     // 必需: 设备ID
  "msg": "play_end",      // 必需: 消息类型
  "data": "additional data"  // 可选: 附加数据
}
```

#### 支持的消息类型
- `query`: 查询消息
- `play_end`: 音频播放结束通知

#### 响应
```json
{
  "status": "success",
  "message": "Message processed"
}
```

#### 示例代码

**JavaScript**
```javascript
function sendMessage(msgType, data) {
  fetch('/message', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({
      id: 'device_123',
      msg: msgType,
      data: data
    })
  })
  .then(response => response.json())
  .then(result => console.log('Success:', result));
}

// 发送播放结束通知
sendMessage('play_end', 'audio playback finished');
```

---

### 3. 健康检查接口

**`GET /health`**

检查服务状态。

#### 响应
```json
{
  "status": "healthy",
  "timestamp": **********,
  "services": {
    "redis": true,
    "kafka": true,
    "task_manager": true,
    "message_handler": true
  }
}
```

---

### 4. 统计信息接口

**`GET /stats`**

获取系统统计信息。

#### 响应
```json
{
  "timestamp": **********,
  "task_manager": {
    "total_tasks": 100,
    "completed_tasks": 95,
    "failed_tasks": 2,
    "pending_tasks": 3
  },
  "message_handler": {
    "kafka_consumer_running": true,
    "sse_stats": {
      "total_sessions": 5,
      "active_sessions": 3,
      "devices_connected": 2
    }
  }
}
```

---

## 🔄 完整工作流程

### 1. 客户端上传文件
```mermaid
sequenceDiagram
    participant Client
    participant Server
    participant TaskManager
    participant Kafka
    
    Client->>Server: POST /upload (audio, image, location)
    Server->>Server: Create SSE session
    Server->>TaskManager: Submit processing task
    Server->>Client: Return SSE stream
    
    TaskManager->>Kafka: Send ASR/Image processing
    Kafka->>TaskManager: Return processing results
    TaskManager->>Kafka: Send final message
    Kafka->>Server: Consume speak message
    Server->>Client: Push audio via SSE
```

### 2. 客户端交互
```mermaid
sequenceDiagram
    participant Client
    participant Server
    
    Client->>Server: Receive audio via SSE
    Client->>Client: Play audio
    Client->>Server: POST /message (play_end)
    Server->>Server: Handle play_end message
```

---

## 🛠️ 错误处理

### HTTP 错误码
- `400 Bad Request`: 请求参数错误
- `500 Internal Server Error`: 服务器内部错误

### SSE 连接错误
- 连接断开时客户端需要重新发起 `/upload` 请求
- 建议实现自动重连机制

### 示例错误响应
```json
{
  "error": "Missing device ID"
}
```

---

## 📝 最佳实践

### 1. 客户端实现建议
- 使用 EventSource API 或手动处理 SSE 流
- 实现自动重连机制
- 正确处理各种事件类型
- 及时发送 `play_end` 消息

### 2. 错误处理
- 监听 SSE 连接错误并重连
- 处理网络超时情况
- 验证服务器响应格式

### 3. 性能优化
- 合理设置音频/图片文件大小限制
- 实现客户端缓存机制
- 监控 SSE 连接状态

---

## 🔧 开发和测试

### 测试工具
- **Python**: `test_sse.py` - 完整功能测试
- **HTML**: `test_sse_new.html` - 浏览器交互测试

### 本地开发
```bash
# 启动服务器
python main.py

# 运行测试
python test_sse.py

# 浏览器测试
open test_sse_new.html
```

---

## 📊 与旧版本对比

| 特性 | 旧版本 (WebSocket) | 新版本 (SSE + HTTP) |
|------|-------------------|---------------------|
| 连接方式 | 双向 WebSocket | SSE + HTTP API |
| 消息推送 | WebSocket 发送 | SSE 事件流 |
| 客户端发送 | WebSocket 发送 | HTTP POST |
| 连接管理 | 复杂的连接状态管理 | 简单的 HTTP 请求 |
| 负载均衡 | 需要粘性会话 | 天然支持 |
| 浏览器兼容性 | 需要 WebSocket 支持 | 更好的兼容性 |

新版本在保持功能完整性的同时，提供了更好的可扩展性和更简单的客户端实现。
