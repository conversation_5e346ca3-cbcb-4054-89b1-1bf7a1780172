# 🚀 新版 SSE 聊天应用 API 文档

## 概述

新版聊天应用采用 **SSE (Server-Sent Events) + HTTP API** 的架构，替代了原来的 WebSocket 双向通信方案。

### 架构特点
- **单向推送**: 服务器通过 SSE 向客户端推送消息
- **HTTP 接收**: 客户端通过 HTTP API 向服务器发送消息
- **简化连接**: 避免了 WebSocket 的连接管理复杂性
- **更好的扩展性**: 支持负载均衡和横向扩展

---

## 🔗 API 端点

### 1. 文件上传接口 (返回 SSE 流)

**`POST /upload`**

上传音频、图片文件并立即返回 SSE 事件流用于接收处理结果。

#### 请求参数
```json
{
  "id": "device_123",           // 必需: 设备ID
  "audio": "data:audio/wav;base64,UklGR...",  // 可选: Base64编码的音频数据
  "video": "data:image/jpeg;base64,/9j/4...", // 可选: Base64编码的图片数据
  "longitude": 116.3974,        // 必需: 经度
  "latitude": 39.9093          // 必需: 纬度
}
```

#### 响应
- **Content-Type**: `text/event-stream`
- **状态码**: 200 (成功) / 400 (参数错误) / 500 (服务器错误)

#### SSE 事件类型

##### `connected` 事件
连接建立成功时发送
```
event: connected
data: {"session_id": "uuid", "device_id": "device_123", "timestamp": 1234567890}
```

##### `audio_message` 事件
返回的音频文本，可以根据"is_end"字段内容判断是否是最后一句，"is_end"为True，表示最后一句
```
event: audio_message
data: {
  "msg_type": "wav",
  "wav": "tmp/audio_file.wav",
  "sent": "处理后的文本内容",
  "meta_data": {...},
  "is_end": false,
  "event_id": "event_123"
}
```

##### `end_message` 事件
所有处理完成时发送
```
event: end_message
data: {
  "msg_type": "end",
  "meta_data": {...},
  "event_id": "event_123"
}
```

##### `heartbeat` 事件
保持连接活跃 (每30秒)
```
event: heartbeat
data: {"timestamp": 1234567890}
```

#### 示例代码

**JavaScript (Fetch API)**
```javascript
const uploadData = {
  id: 'device_123',
  audio: 'data:audio/wav;base64,...',
  video: 'data:image/jpeg;base64,...',
  longitude: 116.3974,
  latitude: 39.9093
};

fetch('/upload', {
  method: 'POST',
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify(uploadData)
})
.then(response => {
  const reader = response.body.getReader();
  const decoder = new TextDecoder();
  
  function readStream() {
    reader.read().then(({done, value}) => {
      if (done) return;
      
      const chunk = decoder.decode(value);
      console.log('SSE Event:', chunk);
      
      readStream();
    });
  }
  
  readStream();
});
```

**Python (requests)**
```python
import requests

upload_data = {
    "id": "device_123",
    "audio": "data:audio/wav;base64,...",
    "video": "data:image/jpeg;base64,...",
    "longitude": 116.3974,
    "latitude": 39.9093
}

response = requests.post(
    'http://localhost:9214/upload',
    json=upload_data,
    stream=True
)

for line in response.iter_lines(decode_unicode=True):
    if line:
        print(f"SSE: {line}")
```

---

### 2. 统计信息接口

**`GET /stats`**

获取系统统计信息。

#### 响应
```json
{
  "timestamp": 1234567890,
  "task_manager": {
    "total_tasks": 100,
    "completed_tasks": 95,
    "failed_tasks": 2,
    "pending_tasks": 3
  },
  "message_handler": {
    "kafka_consumer_running": true,
    "sse_stats": {
      "total_sessions": 5,
      "active_sessions": 3,
      "devices_connected": 2
    }
  }
}
```

---

## 🛠️ 错误处理

### HTTP 错误码
- `400 Bad Request`: 请求参数错误
- `500 Internal Server Error`: 服务器内部错误

### SSE 连接错误
- 连接断开时客户端需要重新发起 `/upload` 请求
- 建议实现自动重连机制

### 示例错误响应
```json
{
  "error": "Missing device ID"
}
```

