#!/usr/bin/env python3
"""
Enhanced startup script with better control and monitoring.
Provides multiple ways to start and stop the application.
"""

import os
import sys
import time
import signal
import argparse
from pathlib import Path

def setup_environment():
    """Setup basic environment and add current directory to Python path."""
    current_dir = Path(__file__).parent.absolute()
    if str(current_dir) not in sys.path:
        sys.path.insert(0, str(current_dir))

def run_application(debug=False, port=None):
    """Run the main application."""
    setup_environment()
    
    # Set debug environment if requested
    if debug:
        os.environ['LOG_LEVEL'] = 'DEBUG'
    
    # Override port if specified
    if port:
        os.environ['APP_PORT'] = str(port)
    
    try:
        from main import main
        return main()
    except KeyboardInterrupt:
        print("\nReceived keyboard interrupt, shutting down...")
        return 0
    except Exception as e:
        print(f"Application error: {e}")
        return 1

def check_dependencies():
    """Check if required dependencies are available."""
    missing_deps = []
    
    try:
        import flask
    except ImportError:
        missing_deps.append('flask')
    
    try:
        import gevent
    except ImportError:
        missing_deps.append('gevent')
    
    try:
        import kafka
    except ImportError:
        missing_deps.append('kafka-python')
    
    try:
        import redis
    except ImportError:
        missing_deps.append('redis')
    
    if missing_deps:
        print("Missing required dependencies:")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("\nInstall them with: pip install -r requirements.txt")
        return False
    
    return True

def check_external_services():
    """Check if external services are accessible."""
    setup_environment()
    
    try:
        from config import config
        
        print("Checking external services...")
        
        # Check Redis
        try:
            import redis
            r = redis.Redis(
                host=config.redis.host,
                port=config.redis.port,
                password=config.redis.password,
                socket_timeout=5
            )
            r.ping()
            print(f"✓ Redis: {config.redis.host}:{config.redis.port}")
        except Exception as e:
            print(f"✗ Redis: {config.redis.host}:{config.redis.port} - {e}")
        
        # Check Kafka
        try:
            from kafka import KafkaProducer
            producer = KafkaProducer(
                bootstrap_servers=config.kafka.brokers,
                request_timeout_ms=5000
            )
            producer.close()
            print(f"✓ Kafka: {config.kafka.brokers}")
        except Exception as e:
            print(f"✗ Kafka: {config.kafka.brokers} - {e}")
        
        # Check TTS service
        try:
            import requests
            response = requests.get(config.external_api.tts_url.replace('/tts', '/health'), timeout=5)
            print(f"✓ TTS: {config.external_api.tts_url}")
        except Exception as e:
            print(f"✗ TTS: {config.external_api.tts_url} - {e}")
        
        # Check ASR service
        try:
            import requests
            response = requests.get(config.external_api.asr_url.replace('/asr_offline', '/health'), timeout=5)
            print(f"✓ ASR: {config.external_api.asr_url}")
        except Exception as e:
            print(f"✗ ASR: {config.external_api.asr_url} - {e}")
            
    except Exception as e:
        print(f"Error checking services: {e}")

def show_config():
    """Show current configuration."""
    setup_environment()
    
    try:
        from config import config
        
        print("Current Configuration:")
        print(f"  App: {config.app.host}:{config.app.port}")
        print(f"  Kafka: {config.kafka.brokers} (topic: {config.kafka.topic})")
        print(f"  Redis: {config.redis.host}:{config.redis.port}")
        print(f"  TTS: {config.external_api.tts_url}")
        print(f"  ASR: {config.external_api.asr_url}")
        print(f"  Static: {config.file.static_tmp_dir}")
        print(f"  Share: {config.file.base_share_dir}")
        
    except Exception as e:
        print(f"Error loading configuration: {e}")

def main():
    """Main entry point for the run script."""
    parser = argparse.ArgumentParser(description='Chat Application Control Script')
    parser.add_argument('command', choices=['start', 'check-deps', 'check-services', 'config'], 
                       help='Command to execute')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    parser.add_argument('--port', type=int, help='Override port number')
    
    args = parser.parse_args()
    
    if args.command == 'start':
        if not check_dependencies():
            return 1
        
        print("Starting chat application...")
        print("Press Ctrl+C to stop")
        print("-" * 50)
        
        return run_application(debug=args.debug, port=args.port)
    
    elif args.command == 'check-deps':
        if check_dependencies():
            print("All dependencies are available.")
            return 0
        else:
            return 1
    
    elif args.command == 'check-services':
        check_external_services()
        return 0
    
    elif args.command == 'config':
        show_config()
        return 0

if __name__ == '__main__':
    sys.exit(main())
