"""
Service connection management module.
Handles connections to Kafka, Redis, and external APIs with proper lifecycle management.
"""

import json
import time
import requests
from typing import Optional, Dict, Any, List
from contextlib import contextmanager
from threading import RLock

import redis
from kafka import KafkaProducer, KafkaConsumer
from loguru import logger

from config import Config
from utils import retry_on_exception, safe_json_dumps, PerformanceTimer


class RedisService:
    """Redis connection service with connection pooling."""
    
    def __init__(self, config: Config):
        self.config = config.redis
        self._pool = None
        self._client = None
        self._lock = RLock()
    
    def initialize(self) -> bool:
        """Initialize Redis connection pool."""
        try:
            self._pool = redis.ConnectionPool(
                host=self.config.host,
                port=self.config.port,
                db=self.config.db,
                password=self.config.password,
                max_connections=self.config.max_connections,
                retry_on_timeout=True,
                socket_keepalive=True,
                socket_keepalive_options={}
            )
            self._client = redis.Redis(connection_pool=self._pool)
            
            # Test connection
            self._client.ping()
            logger.info(f"Redis connected to {self.config.host}:{self.config.port}")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize Redis: {e}")
            return False
    
    @property
    def client(self) -> redis.Redis:
        """Get Redis client instance."""
        if self._client is None:
            raise RuntimeError("Redis service not initialized")
        return self._client
    
    def close(self):
        """Close Redis connections."""
        if self._pool:
            self._pool.disconnect()
            logger.info("Redis connections closed")


class KafkaService:
    """Kafka service for producing and consuming messages."""
    
    def __init__(self, config: Config):
        self.config = config.kafka
        self._producer = None
        self._producer_lock = RLock()
    
    def initialize(self) -> bool:
        """Initialize Kafka producer."""
        try:
            self._producer = self._create_producer()
            logger.info(f"Kafka producer initialized for brokers: {self.config.brokers}")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize Kafka producer: {e}")
            return False
    
    def _create_producer(self) -> KafkaProducer:
        """Create a new Kafka producer instance."""
        return KafkaProducer(
            bootstrap_servers=self.config.brokers,
            key_serializer=lambda k: json.dumps(k).encode() if k is not None else None,
            value_serializer=lambda v: json.dumps(v, ensure_ascii=False).encode(),
            acks=self.config.producer_acks,
            linger_ms=self.config.producer_linger_ms,
            max_request_size=self.config.max_request_size,
            retries=3,
            retry_backoff_ms=100
        )
    
    @retry_on_exception(max_retries=3, delay=0.5)
    def send_message(self, value: Dict[Any, Any], key: Any = None) -> bool:
        """
        Send a message to Kafka topic.
        
        Args:
            value: Message value (will be JSON serialized)
            key: Message key (optional)
            
        Returns:
            True if message was sent successfully
        """
        if self._producer is None:
            logger.error("Kafka producer not initialized")
            return False
        
        try:
            with self._producer_lock:
                future = self._producer.send(
                    topic=self.config.topic,
                    key=key if key is not None else time.time(),
                    value=value
                )
                # Don't wait for the result to avoid blocking
                logger.debug(f"Kafka message sent: {safe_json_dumps(value)[:100]}...")
                return True
        except Exception as e:
            logger.error(f"Failed to send Kafka message: {e}")
            # Try to recreate producer on error
            try:
                with self._producer_lock:
                    if self._producer:
                        self._producer.close()
                    self._producer = self._create_producer()
                    logger.info("Kafka producer recreated after error")
            except Exception as recreate_error:
                logger.error(f"Failed to recreate Kafka producer: {recreate_error}")
            raise
    
    def create_consumer(self, group_id: Optional[str] = None) -> KafkaConsumer:
        """
        Create a new Kafka consumer instance.

        Args:
            group_id: Consumer group ID (uses config default if None)

        Returns:
            KafkaConsumer instance
        """
        def safe_deserializer(m):
            if m is None:
                return None
            try:
                return json.loads(m.decode('utf-8'))
            except Exception as e:
                logger.warning(f"Failed to deserialize Kafka message: {e}")
                return None

        return KafkaConsumer(
            self.config.topic,
            bootstrap_servers=self.config.brokers,
            auto_offset_reset='latest',
            group_id=group_id or self.config.consumer_group,
            enable_auto_commit=True,
            max_poll_interval_ms=self.config.max_poll_interval_ms,
            value_deserializer=safe_deserializer
        )
    
    def close(self):
        """Close Kafka producer."""
        if self._producer:
            self._producer.close()
            logger.info("Kafka producer closed")


class ExternalAPIService:
    """Service for external API calls (TTS, ASR, Exhibition)."""
    
    def __init__(self, config: Config):
        self.config = config.external_api
        self._session = None
    
    def initialize(self) -> bool:
        """Initialize HTTP session with connection pooling."""
        try:
            self._session = requests.Session()
            # Configure connection pooling
            adapter = requests.adapters.HTTPAdapter(
                pool_connections=10,
                pool_maxsize=20,
                max_retries=3
            )
            self._session.mount('http://', adapter)
            self._session.mount('https://', adapter)
            logger.info("External API service initialized")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize external API service: {e}")
            return False
    
    @retry_on_exception(max_retries=2, delay=0.5)
    def text_to_speech(self, text: str, format: str = 'aac') -> Optional[bytes]:
        """
        Convert text to speech using TTS API.
        
        Args:
            text: Text to convert
            format: Audio format
            
        Returns:
            Audio data as bytes, or None on error
        """
        if not text or not self._session:
            return None
        
        data = {"text": text, "format": format}
        
        with PerformanceTimer(f"TTS request for {len(text)} chars"):
            try:
                response = self._session.post(
                    self.config.tts_url,
                    data=data,
                    timeout=self.config.tts_timeout
                )
                
                if response.status_code == 200:
                    content_type = response.headers.get("Content-Type", "")
                    if content_type.startswith("audio/") or isinstance(response.content, (bytes, bytearray)):
                        if response.content:
                            return response.content
                        else:
                            logger.error("TTS returned empty audio")
                    else:
                        logger.error(f"TTS unexpected content-type: {content_type}")
                else:
                    logger.error(f"TTS HTTP {response.status_code}: {response.text[:200]}")
                
            except requests.exceptions.Timeout:
                logger.error("TTS request timeout")
            except Exception as e:
                logger.error(f"TTS request failed: {e}")
        
        return None
    
    @retry_on_exception(max_retries=2, delay=0.5)
    def speech_to_text(self, audio_data: bytes) -> Optional[str]:
        """
        Convert speech to text using ASR API.
        
        Args:
            audio_data: Audio data as bytes
            
        Returns:
            Recognized text, or None on error
        """
        if not audio_data or not self._session:
            return None
        
        with PerformanceTimer(f"ASR request for {len(audio_data)} bytes"):
            try:
                response = self._session.post(
                    self.config.asr_url,
                    data=audio_data,
                    timeout=self.config.asr_timeout
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        text = "".join([d.get('text', '') for d in result.get('results', [])])
                        return text
                    else:
                        logger.warning(f"ASR returned success=false: {result}")
                else:
                    logger.error(f"ASR HTTP {response.status_code}: {response.text[:200]}")
                    
            except requests.exceptions.Timeout:
                logger.error("ASR request timeout")
            except Exception as e:
                logger.error(f"ASR request failed: {e}")
        
        return None
    
    @retry_on_exception(max_retries=2, delay=0.5)
    def get_exhibition_info(self, longitude: float, latitude: float) -> tuple[Optional[str], Optional[str]]:
        """
        Get exhibition information based on coordinates.
        
        Args:
            longitude: Longitude coordinate
            latitude: Latitude coordinate
            
        Returns:
            Tuple of (exhibition_id, exhibition_name) or (None, None)
        """
        if not self._session:
            return None, None
        
        headers = {'content-type': 'application/json;charset=utf-8'}
        data = {
            'from': "client",
            "to": "rag",
            "user id": "test user",
            "device id": "test device",
            "Timestamp": ""
        }
        
        try:
            response = self._session.post(
                self.config.exhibition_api_url,
                json=data,
                headers=headers,
                timeout=self.config.exhibition_timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                return self._find_best_exhibition(result, longitude, latitude)
            else:
                logger.error(f"Exhibition API HTTP {response.status_code}: {response.text[:200]}")
                
        except Exception as e:
            logger.error(f"Exhibition API request failed: {e}")
        
        return None, None
    
    def _find_best_exhibition(self, result: Dict, longitude: float, latitude: float) -> tuple[Optional[str], Optional[str]]:
        """Find the best matching exhibition based on location."""
        from utils import haversine
        
        best = None
        best_dist = None
        target_id = None
        target_name = "人民网综合展厅"
        
        for exhibition in result.get('exhibition_list', []):
            eid = exhibition.get('exhibition_id')
            name = exhibition.get('name')
            lon = exhibition.get('longitude')
            lat = exhibition.get('latitude')
            
            if name == target_name:
                target_id = eid
            
            if lon is None or lat is None:
                continue
            
            dist = haversine(longitude, latitude, lon, lat)
            if best is None or dist < best_dist:
                best = (eid, name)
                best_dist = dist
        
        if best:
            eid, name = best
            if "人民网" in name and target_id:
                return target_id, target_name
            return eid, name
        
        return None, None
    
    def close(self):
        """Close HTTP session."""
        if self._session:
            self._session.close()
            logger.info("External API service closed")


class ServiceManager:
    """Central service manager for all external services."""
    
    def __init__(self, config: Config):
        self.config = config
        self.redis = RedisService(config)
        self.kafka = KafkaService(config)
        self.external_api = ExternalAPIService(config)
        self._initialized = False
    
    def initialize_all(self) -> bool:
        """Initialize all services."""
        services = [
            ("Redis", self.redis),
            ("Kafka", self.kafka),
            ("External API", self.external_api)
        ]
        
        success = True
        for name, service in services:
            if not service.initialize():
                logger.error(f"Failed to initialize {name} service")
                success = False
        
        self._initialized = success
        if success:
            logger.info("All services initialized successfully")
        
        return success
    
    def close_all(self):
        """Close all service connections."""
        services = [self.redis, self.kafka, self.external_api]
        for service in services:
            try:
                service.close()
            except Exception as e:
                logger.error(f"Error closing service: {e}")
        
        logger.info("All services closed")
    
    @property
    def is_initialized(self) -> bool:
        """Check if all services are initialized."""
        return self._initialized
