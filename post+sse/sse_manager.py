"""
Server-Sent Events (SSE) management module.
Handles SSE connections, event streaming, and client session management.
"""

import json
import time
import uuid
from typing import Dict, Any, Optional, Generator, Set
from threading import RLock
from collections import deque
import gevent
from flask import Response
from loguru import logger

from config import Config
from utils import safe_json_dumps


class SSESession:
    """Represents an SSE session for a client."""
    
    def __init__(self, session_id: str, device_id: str):
        self.session_id = session_id
        self.device_id = device_id
        self.created_time = time.time()
        self.last_activity = time.time()
        self.event_queue = deque(maxlen=100)  # Limit queue size
        self.is_active = True
        self._lock = RLock()
    
    def add_event(self, event_type: str, data: Dict[str, Any], event_id: Optional[str] = None) -> None:
        """Add an event to the session queue."""
        with self._lock:
            if not self.is_active:
                return
            
            event = {
                'id': event_id or str(uuid.uuid4()),
                'event': event_type,
                'data': data,
                'timestamp': time.time()
            }
            
            self.event_queue.append(event)
            self.last_activity = time.time()
            logger.debug(f"Added event to session {self.session_id}: {event_type}")
    
    def get_events(self) -> list:
        """Get all pending events and clear the queue."""
        with self._lock:
            events = list(self.event_queue)
            self.event_queue.clear()
            self.last_activity = time.time()
            return events
    
    def close(self) -> None:
        """Close the session."""
        with self._lock:
            self.is_active = False
            self.event_queue.clear()


class SSEManager:
    """Manages SSE sessions and event streaming."""
    
    def __init__(self, config: Config):
        self.config = config
        self.sessions: Dict[str, SSESession] = {}
        self.device_sessions: Dict[str, Set[str]] = {}  # device_id -> set of session_ids
        self.sessions_lock = RLock()
        self.session_timeout = 300  # 5 minutes
        
        # Start cleanup task
        gevent.spawn(self._cleanup_loop)
    
    def create_session(self, device_id: str) -> str:
        """
        Create a new SSE session for a device.
        
        Args:
            device_id: Device identifier
            
        Returns:
            Session ID
        """
        session_id = str(uuid.uuid4())
        
        with self.sessions_lock:
            session = SSESession(session_id, device_id)
            self.sessions[session_id] = session
            
            # Track sessions by device
            if device_id not in self.device_sessions:
                self.device_sessions[device_id] = set()
            self.device_sessions[device_id].add(session_id)
        
        logger.info(f"Created SSE session {session_id} for device {device_id}")
        return session_id
    
    def get_session(self, session_id: str) -> Optional[SSESession]:
        """Get a session by ID."""
        with self.sessions_lock:
            return self.sessions.get(session_id)
    
    def close_session(self, session_id: str) -> None:
        """Close and remove a session."""
        with self.sessions_lock:
            session = self.sessions.get(session_id)
            if session:
                device_id = session.device_id
                session.close()
                
                # Remove from sessions
                del self.sessions[session_id]
                
                # Remove from device tracking
                if device_id in self.device_sessions:
                    self.device_sessions[device_id].discard(session_id)
                    if not self.device_sessions[device_id]:
                        del self.device_sessions[device_id]
                
                logger.info(f"Closed SSE session {session_id} for device {device_id}")
    
    def send_event_to_device(self, device_id: str, event_type: str, 
                           data: Dict[str, Any], event_id: Optional[str] = None) -> int:
        """
        Send an event to all sessions of a specific device.
        
        Args:
            device_id: Target device ID
            event_type: Event type
            data: Event data
            event_id: Optional event ID
            
        Returns:
            Number of sessions that received the event
        """
        sent_count = 0
        
        with self.sessions_lock:
            session_ids = self.device_sessions.get(device_id, set()).copy()
        
        for session_id in session_ids:
            session = self.get_session(session_id)
            if session and session.is_active:
                session.add_event(event_type, data, event_id)
                sent_count += 1
        
        if sent_count > 0:
            logger.debug(f"Sent event '{event_type}' to {sent_count} sessions for device {device_id}")
        else:
            logger.warning(f"No active sessions found for device {device_id}")
        
        return sent_count
    
    def create_event_stream(self, session_id: str) -> Generator[str, None, None]:
        """
        Create an SSE event stream for a session.
        
        Args:
            session_id: Session ID
            
        Yields:
            SSE formatted event strings
        """
        session = self.get_session(session_id)
        if not session:
            logger.warning(f"Session {session_id} not found")
            return
        
        logger.info(f"Starting SSE stream for session {session_id}")
        
        try:
            # Send initial connection event
            yield self._format_sse_event('connected', {
                'session_id': session_id,
                'device_id': session.device_id,
                'timestamp': time.time()
            })
            
            # Keep the connection alive and send events
            while session.is_active:
                events = session.get_events()
                
                if events:
                    for event in events:
                        yield self._format_sse_event(
                            event['event'],
                            event['data'],
                            event['id']
                        )
                else:
                    # Send heartbeat every 30 seconds
                    yield self._format_sse_event('heartbeat', {
                        'timestamp': time.time()
                    })
                # if event['event'] == 'end_message':
                #     session.is_active = False
                #     break
                
                # Sleep to prevent busy waiting
                gevent.sleep(1)
            logger.info(f"Session {session_id} disconnected")
        
        except Exception as e:
            logger.error(f"Error in SSE stream for session {session_id}: {e}")
        
        finally:
            logger.info(f"SSE stream ended for session {session_id}")
            self.close_session(session_id)
    
    def _format_sse_event(self, event_type: str, data: Dict[str, Any], 
                         event_id: Optional[str] = None) -> str:
        """
        Format data as SSE event.
        
        Args:
            event_type: Event type
            data: Event data
            event_id: Optional event ID
            
        Returns:
            SSE formatted string
        """
        lines = []
        
        if event_id:
            lines.append(f"id: {event_id}")
        
        lines.append(f"event: {event_type}")
        lines.append(f"data: {safe_json_dumps(data)}")
        lines.append("")  # Empty line to end the event
        
        return "\n".join(lines) + "\n"
    
    def _cleanup_loop(self) -> None:
        """Background task to clean up expired sessions."""
        while True:
            try:
                current_time = time.time()
                expired_sessions = []
                
                with self.sessions_lock:
                    for session_id, session in self.sessions.items():
                        if current_time - session.last_activity > self.session_timeout:
                            expired_sessions.append(session_id)
                
                for session_id in expired_sessions:
                    logger.info(f"Cleaning up expired session {session_id}")
                    self.close_session(session_id)
                
                if expired_sessions:
                    logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")
                
            except Exception as e:
                logger.error(f"Error in SSE cleanup loop: {e}")
            
            # Run cleanup every minute
            gevent.sleep(60)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get SSE manager statistics."""
        with self.sessions_lock:
            active_sessions = sum(1 for s in self.sessions.values() if s.is_active)
            total_events = sum(len(s.event_queue) for s in self.sessions.values())
            
            return {
                'total_sessions': len(self.sessions),
                'active_sessions': active_sessions,
                'devices_connected': len(self.device_sessions),
                'pending_events': total_events,
                'session_timeout': self.session_timeout
            }
    
    def broadcast_to_all(self, event_type: str, data: Dict[str, Any]) -> int:
        """
        Broadcast an event to all active sessions.
        
        Args:
            event_type: Event type
            data: Event data
            
        Returns:
            Number of sessions that received the event
        """
        sent_count = 0
        
        with self.sessions_lock:
            session_ids = list(self.sessions.keys())
        
        for session_id in session_ids:
            session = self.get_session(session_id)
            if session and session.is_active:
                session.add_event(event_type, data)
                sent_count += 1
        
        logger.info(f"Broadcast event '{event_type}' to {sent_count} sessions")
        return sent_count
