#!/usr/bin/env python3
"""
Simple test script to verify the message_handler.py modifications.
Tests the basic functionality without requiring external services.
"""

import sys
import time
from unittest.mock import Mock, MagicMock

# Mock external dependencies
sys.modules['loguru'] = Mock()
sys.modules['gevent'] = Mock()
sys.modules['config'] = Mock()
sys.modules['services'] = Mock()
sys.modules['audio_processor'] = Mock()
sys.modules['sse_manager'] = Mock()
sys.modules['utils'] = Mock()

# Import after mocking
from message_handler import MessageHandler

def test_message_handler_initialization():
    """Test that MessageHandler can be initialized with SSEManager."""
    print("Testing MessageHandler initialization...")
    
    # Create mock objects
    mock_config = Mock()
    mock_service_manager = Mock()
    mock_audio_processor = Mock()
    mock_sse_manager = Mock()
    
    # Initialize MessageHandler
    handler = MessageHandler(
        mock_config,
        mock_service_manager,
        mock_audio_processor,
        mock_sse_manager
    )
    
    # Verify initialization
    assert handler.config == mock_config
    assert handler.service_manager == mock_service_manager
    assert handler.audio_processor == mock_audio_processor
    assert handler.sse_manager == mock_sse_manager
    assert handler._consumer is None
    assert handler._running is False
    
    print("✅ MessageHandler initialization test passed")

def test_handle_client_message():
    """Test the handle_client_message method."""
    print("Testing handle_client_message...")
    
    # Create mock objects
    mock_config = Mock()
    mock_service_manager = Mock()
    mock_audio_processor = Mock()
    mock_sse_manager = Mock()
    
    # Initialize MessageHandler
    handler = MessageHandler(
        mock_config,
        mock_service_manager,
        mock_audio_processor,
        mock_sse_manager
    )
    
    # Test different message types
    test_messages = [
        {"msg": "query", "data": "test query"},
        {"msg": "play_end", "data": "playback finished"},
        {"msg": "unknown", "data": "unknown message type"}
    ]
    
    for message in test_messages:
        try:
            handler.handle_client_message("test_device", message)
            print(f"✅ Handled message type: {message['msg']}")
        except Exception as e:
            print(f"❌ Error handling message type {message['msg']}: {e}")
    
    print("✅ handle_client_message test completed")

def test_get_message_stats():
    """Test the get_message_stats method."""
    print("Testing get_message_stats...")
    
    # Create mock objects
    mock_config = Mock()
    mock_service_manager = Mock()
    mock_audio_processor = Mock()
    mock_sse_manager = Mock()
    
    # Mock SSE manager stats
    mock_sse_manager.get_stats.return_value = {
        'total_sessions': 5,
        'active_sessions': 3,
        'devices_connected': 2
    }
    
    # Initialize MessageHandler
    handler = MessageHandler(
        mock_config,
        mock_service_manager,
        mock_audio_processor,
        mock_sse_manager
    )
    
    # Get stats
    stats = handler.get_message_stats()
    
    # Verify stats structure
    assert 'kafka_consumer_running' in stats
    assert 'sse_stats' in stats
    assert stats['kafka_consumer_running'] is False  # Not started yet
    assert stats['sse_stats']['total_sessions'] == 5
    
    print("✅ get_message_stats test passed")

def main():
    """Run all tests."""
    print("Running MessageHandler tests...")
    print("=" * 50)
    
    try:
        test_message_handler_initialization()
        test_handle_client_message()
        test_get_message_stats()
        
        print("=" * 50)
        print("🎉 All tests passed! MessageHandler modifications are working correctly.")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
