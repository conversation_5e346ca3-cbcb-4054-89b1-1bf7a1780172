<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New SSE Chat Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.connected { background-color: #d4edda; color: #155724; }
        .status.disconnected { background-color: #f8d7da; color: #721c24; }
        .status.processing { background-color: #fff3cd; color: #856404; }
        
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        
        .message {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        .message.upload { background-color: #e3f2fd; }
        .message.sse { background-color: #f3e5f5; }
        .message.api { background-color: #e8f5e8; }
        .message.error { background-color: #ffebee; color: #c62828; }
        
        input, select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
        
        .form-group {
            margin: 10px 0;
        }
        
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        
        .stat-item {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        
        .audio-player {
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🚀 New SSE Chat Test</h1>
    
    <div class="container">
        <h2>连接状态</h2>
        <div id="status" class="status disconnected">未连接</div>
        <div class="stats">
            <div class="stat-item">
                <div class="stat-value" id="totalEvents">0</div>
                <div>总事件数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="audioEvents">0</div>
                <div>音频事件</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="duration">0s</div>
                <div>连接时长</div>
            </div>
        </div>
    </div>
    
    <div class="container">
        <h2>设备配置</h2>
        <div class="form-group">
            <label>设备ID:</label>
            <input type="text" id="deviceId" value="test_device_sse_123">
        </div>
        <div class="form-group">
            <label>经度:</label>
            <input type="number" id="longitude" value="116.3974" step="0.0001">
        </div>
        <div class="form-group">
            <label>纬度:</label>
            <input type="number" id="latitude" value="39.9093" step="0.0001">
        </div>
    </div>
    
    <div class="container">
        <h2>文件上传测试 (返回 SSE 流)</h2>
        <div class="form-group">
            <label>音频文件:</label>
            <input type="file" id="audioFile" accept="audio/*">
        </div>
        <div class="form-group">
            <label>图片文件:</label>
            <input type="file" id="imageFile" accept="image/*">
        </div>
        <button onclick="uploadFiles()" id="uploadBtn">上传文件 (SSE)</button>
        <button onclick="uploadTestData()">上传测试数据 (SSE)</button>
        <button onclick="stopSSE()" id="stopBtn" disabled>停止 SSE 连接</button>
    </div>
    
    <div class="container">
        <h2>HTTP API 消息测试</h2>
        <button onclick="sendMessage('query', 'test query')" id="queryBtn">发送查询</button>
        <button onclick="sendMessage('play_end', 'audio finished')" id="playEndBtn">发送播放结束</button>
    </div>
    
    <div class="container">
        <h2>接收到的音频</h2>
        <div id="audioContainer"></div>
    </div>
    
    <div class="container">
        <h2>消息日志</h2>
        <button onclick="clearLog()">清空日志</button>
        <div id="log" class="log"></div>
    </div>

    <script>
        let eventSource = null;
        let deviceId = 'test_device_sse_123';
        let startTime = null;
        let stats = {
            totalEvents: 0,
            audioEvents: 0
        };
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            logDiv.appendChild(messageDiv);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function updateStatus(status, className) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = status;
            statusDiv.className = `status ${className}`;
        }
        
        function updateStats() {
            document.getElementById('totalEvents').textContent = stats.totalEvents;
            document.getElementById('audioEvents').textContent = stats.audioEvents;
            
            if (startTime) {
                const duration = Math.floor((Date.now() - startTime) / 1000);
                document.getElementById('duration').textContent = `${duration}s`;
            }
        }
        
        function updateButtons(connected) {
            document.getElementById('stopBtn').disabled = !connected;
        }
        
        function setupSSE() {
            if (eventSource) {
                eventSource.close();
            }
            
            startTime = Date.now();
            stats = { totalEvents: 0, audioEvents: 0 };
            updateStats();
            
            log('🔄 设置 SSE 连接...', 'sse');
            updateStatus('连接中...', 'processing');
            updateButtons(false);
            
            // SSE 事件处理
            const handleSSEEvent = (event) => {
                stats.totalEvents++;
                updateStats();
                
                try {
                    const data = JSON.parse(event.data);
                    log(`📨 SSE 事件: ${event.type} - ${JSON.stringify(data)}`, 'sse');
                    
                    // 处理音频消息
                    if (event.type === 'audio_message' && data.wav) {
                        stats.audioEvents++;
                        updateStats();
                        
                        // 创建音频播放器
                        const audioContainer = document.getElementById('audioContainer');
                        const audioDiv = document.createElement('div');
                        audioDiv.className = 'audio-player';
                        audioDiv.innerHTML = `
                            <p>🎵 音频: ${data.sent || 'Unknown'}</p>
                            <audio controls>
                                <source src="/${data.wav}" type="audio/wav">
                                您的浏览器不支持音频播放。
                            </audio>
                        `;
                        audioContainer.appendChild(audioDiv);
                        
                        // 自动发送播放结束消息
                        setTimeout(() => {
                            sendMessage('play_end', 'audio playback finished');
                        }, 2000);
                    }
                    
                    // 处理结束消息
                    if (event.type === 'end_message') {
                        log('🎉 收到结束消息，处理完成！', 'sse');
                        updateStatus('处理完成', 'connected');
                    }
                    
                } catch (e) {
                    log(`❌ 解析 SSE 数据失败: ${event.data}`, 'error');
                }
            };
            
            return new Promise((resolve, reject) => {
                // 设置事件监听器
                eventSource.addEventListener('connected', (event) => {
                    log('✅ SSE 连接成功', 'sse');
                    updateStatus('已连接', 'connected');
                    updateButtons(true);
                    handleSSEEvent(event);
                    resolve();
                });
                
                eventSource.addEventListener('audio_message', handleSSEEvent);
                eventSource.addEventListener('end_message', handleSSEEvent);
                eventSource.addEventListener('heartbeat', handleSSEEvent);
                
                eventSource.onerror = (error) => {
                    log(`❌ SSE 连接错误: ${error}`, 'error');
                    updateStatus('连接错误', 'disconnected');
                    updateButtons(false);
                    reject(error);
                };
                
                eventSource.onopen = () => {
                    log('🔗 SSE 连接已打开', 'sse');
                };
                
                eventSource.onclose = () => {
                    log('🔌 SSE 连接已关闭', 'sse');
                    updateStatus('未连接', 'disconnected');
                    updateButtons(false);
                };
            });
        }
        
        function stopSSE() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
                log('🛑 SSE 连接已停止', 'sse');
            }
        }
        
        function fileToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = () => resolve(reader.result);
                reader.onerror = error => reject(error);
            });
        }
        
        async function uploadFiles() {
            const audioFile = document.getElementById('audioFile').files[0];
            const imageFile = document.getElementById('imageFile').files[0];
            
            if (!audioFile && !imageFile) {
                log('❌ 请选择至少一个文件', 'error');
                return;
            }
            
            try {
                deviceId = document.getElementById('deviceId').value;
                
                const uploadData = {
                    id: deviceId,
                    longitude: parseFloat(document.getElementById('longitude').value),
                    latitude: parseFloat(document.getElementById('latitude').value)
                };
                
                if (audioFile) {
                    uploadData.audio = await fileToBase64(audioFile);
                }
                
                if (imageFile) {
                    uploadData.video = await fileToBase64(imageFile);
                }
                
                log('📤 上传文件并建立 SSE 连接...', 'upload');
                
                const response = await fetch('/upload', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(uploadData)
                });
                
                if (response.ok && response.headers.get('content-type')?.includes('text/event-stream')) {
                    log('✅ 文件上传成功，SSE 流已建立', 'upload');
                    
                    // 创建 EventSource 从响应流
                    eventSource = new EventSource('/upload', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(uploadData)
                    });
                    
                    await setupSSE();
                    
                } else {
                    const errorText = await response.text();
                    log(`❌ 文件上传失败: ${response.status} - ${errorText}`, 'error');
                }
                
            } catch (error) {
                log(`❌ 上传错误: ${error}`, 'error');
            }
        }
        
        async function uploadTestData() {
            // 创建测试数据
            const testAudio = 'data:audio/wav;base64,UklGRiQIAABXQVZFZm10IBAAAAABAAEAQB8AAIA+AAACABAAZGF0YQAIAAAA';
            const testImage = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
            
            deviceId = document.getElementById('deviceId').value;
            
            const uploadData = {
                id: deviceId,
                audio: testAudio,
                video: testImage,
                longitude: parseFloat(document.getElementById('longitude').value),
                latitude: parseFloat(document.getElementById('latitude').value)
            };
            
            try {
                log('📤 上传测试数据并建立 SSE 连接...', 'upload');
                
                const response = await fetch('/upload', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(uploadData)
                });
                
                if (response.ok && response.headers.get('content-type')?.includes('text/event-stream')) {
                    log('✅ 测试数据上传成功，处理 SSE 流...', 'upload');
                    
                    // 手动处理 SSE 流
                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    
                    startTime = Date.now();
                    stats = { totalEvents: 0, audioEvents: 0 };
                    updateStatus('处理中...', 'processing');
                    updateButtons(true);
                    
                    try {
                        while (true) {
                            const { done, value } = await reader.read();
                            if (done) break;
                            
                            const chunk = decoder.decode(value);
                            const lines = chunk.split('\n');
                            
                            for (const line of lines) {
                                if (line.trim()) {
                                    log(`📨 SSE: ${line}`, 'sse');
                                    stats.totalEvents++;
                                    updateStats();
                                    
                                    if (line.includes('event: audio_message')) {
                                        stats.audioEvents++;
                                        updateStats();
                                    }
                                    
                                    if (line.includes('event: end_message')) {
                                        log('🎉 收到结束消息，处理完成！', 'sse');
                                        updateStatus('处理完成', 'connected');
                                        return;
                                    }
                                }
                            }
                        }
                    } finally {
                        reader.releaseLock();
                        updateButtons(false);
                    }
                    
                } else {
                    const errorText = await response.text();
                    log(`❌ 测试数据上传失败: ${response.status} - ${errorText}`, 'error');
                }
                
            } catch (error) {
                log(`❌ 上传错误: ${error}`, 'error');
                updateButtons(false);
            }
        }
        
        async function sendMessage(msgType, data) {
            try {
                const message = {
                    id: deviceId,
                    msg: msgType,
                    data: data
                };
                
                log(`📤 发送消息: ${JSON.stringify(message)}`, 'api');
                
                const response = await fetch('/message', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(message)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    log(`✅ 消息发送成功: ${JSON.stringify(result)}`, 'api');
                } else {
                    const errorText = await response.text();
                    log(`❌ 消息发送失败: ${response.status} - ${errorText}`, 'error');
                }
                
            } catch (error) {
                log(`❌ 发送消息错误: ${error}`, 'error');
            }
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            document.getElementById('audioContainer').innerHTML = '';
        }
        
        // 页面加载完成
        window.onload = function() {
            log('🚀 新版 SSE 聊天测试页面加载完成');
            
            // 定时更新统计信息
            setInterval(updateStats, 1000);
        };
        
        // 页面关闭时清理
        window.onbeforeunload = function() {
            stopSSE();
        };
    </script>
</body>
</html>
