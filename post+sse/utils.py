"""
Utility functions for the chat application.
Contains common helper functions used across different modules.
"""

import os
import time
import math
import hashlib
import traceback
from random import randint
from typing import Optional, Tuple, Any, Dict
from functools import wraps
from loguru import logger


def get_file_name(extension: str = 'jpg') -> str:
    """
    Generate a unique filename with the given extension.
    
    Args:
        extension: File extension without the dot
        
    Returns:
        Unique filename with extension
    """
    file_name = f"{int(time.time()*1000)}{randint(1000,9999)}"
    return hashlib.md5(file_name.encode('utf8')).hexdigest() + '.' + extension


def haversine(lon1: float, lat1: float, lon2: float, lat2: float) -> float:
    """
    Calculate the great circle distance between two points on Earth.
    
    Args:
        lon1, lat1: Longitude and latitude of first point in decimal degrees
        lon2, lat2: Longitude and latitude of second point in decimal degrees
        
    Returns:
        Distance in meters
    """
    # Earth's radius in meters
    R = 6371000.0
    
    # Convert to radians
    phi1 = math.radians(lat1)
    phi2 = math.radians(lat2)
    dphi = math.radians(lat2 - lat1)
    dlambda = math.radians(lon2 - lon1)
    
    # Haversine formula
    a = (math.sin(dphi/2)**2 + 
         math.cos(phi1) * math.cos(phi2) * math.sin(dlambda/2)**2)
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
    
    return R * c


def ensure_directory_exists(directory: str) -> bool:
    """
    Ensure a directory exists, create it if it doesn't.
    
    Args:
        directory: Directory path to check/create
        
    Returns:
        True if directory exists or was created successfully
    """
    try:
        os.makedirs(directory, exist_ok=True)
        return True
    except Exception as e:
        logger.error(f"Failed to create directory {directory}: {e}")
        return False


def safe_remove_file(file_path: str) -> bool:
    """
    Safely remove a file with error handling.
    
    Args:
        file_path: Path to the file to remove
        
    Returns:
        True if file was removed or didn't exist, False on error
    """
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            logger.debug(f"Removed file: {file_path}")
        return True
    except Exception as e:
        logger.warning(f"Failed to remove file {file_path}: {e}")
        return False


def get_file_age_seconds(file_path: str) -> Optional[float]:
    """
    Get the age of a file in seconds.
    
    Args:
        file_path: Path to the file
        
    Returns:
        Age in seconds, or None if file doesn't exist or error occurred
    """
    try:
        if not os.path.exists(file_path):
            return None
        mtime = os.path.getmtime(file_path)
        return time.time() - mtime
    except Exception as e:
        logger.warning(f"Failed to get file age for {file_path}: {e}")
        return None


def retry_on_exception(max_retries: int = 3, delay: float = 1.0, 
                      backoff: float = 2.0, exceptions: Tuple = (Exception,)):
    """
    Decorator to retry a function on specified exceptions.
    
    Args:
        max_retries: Maximum number of retry attempts
        delay: Initial delay between retries in seconds
        backoff: Multiplier for delay after each retry
        exceptions: Tuple of exception types to catch and retry on
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            current_delay = delay
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt == max_retries:
                        logger.error(f"Function {func.__name__} failed after {max_retries} retries: {e}")
                        raise e
                    
                    logger.warning(f"Function {func.__name__} failed on attempt {attempt + 1}, "
                                 f"retrying in {current_delay}s: {e}")
                    time.sleep(current_delay)
                    current_delay *= backoff
            
            # This should never be reached, but just in case
            raise last_exception
        
        return wrapper
    return decorator


def safe_json_loads(data: str, default: Any = None) -> Any:
    """
    Safely parse JSON data with error handling.
    
    Args:
        data: JSON string to parse
        default: Default value to return on error
        
    Returns:
        Parsed JSON data or default value
    """
    try:
        import json
        return json.loads(data)
    except Exception as e:
        logger.warning(f"Failed to parse JSON: {e}")
        return default


def safe_json_dumps(data: Any, default: str = "{}") -> str:
    """
    Safely serialize data to JSON with error handling.
    
    Args:
        data: Data to serialize
        default: Default JSON string to return on error
        
    Returns:
        JSON string or default value
    """
    try:
        import json
        return json.dumps(data, ensure_ascii=False)
    except Exception as e:
        logger.warning(f"Failed to serialize JSON: {e}")
        return default


def log_execution_time(func):
    """
    Decorator to log function execution time.
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.debug(f"Function {func.__name__} executed in {execution_time:.3f}s")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Function {func.__name__} failed after {execution_time:.3f}s: {e}")
            raise
    
    return wrapper


def validate_coordinates(longitude: Optional[float], latitude: Optional[float]) -> bool:
    """
    Validate longitude and latitude coordinates.
    
    Args:
        longitude: Longitude value
        latitude: Latitude value
        
    Returns:
        True if coordinates are valid
    """
    if longitude is None or latitude is None:
        return False
    
    try:
        lon = float(longitude)
        lat = float(latitude)
        return -180 <= lon <= 180 and -90 <= lat <= 90
    except (ValueError, TypeError):
        return False


def truncate_string(text: str, max_length: int = 200, suffix: str = "...") -> str:
    """
    Truncate a string to a maximum length with optional suffix.
    
    Args:
        text: String to truncate
        max_length: Maximum length including suffix
        suffix: Suffix to add when truncating
        
    Returns:
        Truncated string
    """
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix


class PerformanceTimer:
    """Context manager for measuring execution time."""
    
    def __init__(self, name: str = "Operation"):
        self.name = name
        self.start_time = None
        self.end_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.time()
        duration = self.end_time - self.start_time
        if exc_type is None:
            logger.debug(f"{self.name} completed in {duration:.3f}s")
        else:
            logger.error(f"{self.name} failed after {duration:.3f}s")
    
    @property
    def duration(self) -> Optional[float]:
        """Get the duration in seconds, or None if not completed."""
        if self.start_time is None:
            return None
        end = self.end_time or time.time()
        return end - self.start_time
