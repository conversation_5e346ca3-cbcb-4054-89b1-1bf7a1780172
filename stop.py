#!/usr/bin/env python3
"""
Stop script for the chat application.
Finds and gracefully terminates running instances.
"""

import os
import sys
import time
import signal
import psutil
from pathlib import Path

def find_app_processes():
    """Find running chat application processes."""
    processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = proc.info['cmdline']
            if cmdline and any('main.py' in arg or 'run.py' in arg for arg in cmdline):
                if any('python' in arg for arg in cmdline):
                    processes.append(proc)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    return processes

def stop_process(proc, timeout=10):
    """Stop a process gracefully."""
    try:
        print(f"Stopping process {proc.pid} ({' '.join(proc.cmdline())})")
        
        # Send SIGTERM first
        proc.terminate()
        
        # Wait for graceful shutdown
        try:
            proc.wait(timeout=timeout)
            print(f"Process {proc.pid} stopped gracefully")
            return True
        except psutil.TimeoutExpired:
            print(f"Process {proc.pid} didn't stop gracefully, forcing...")
            proc.kill()
            proc.wait(timeout=5)
            print(f"Process {proc.pid} killed")
            return True
            
    except psutil.NoSuchProcess:
        print(f"Process {proc.pid} already stopped")
        return True
    except Exception as e:
        print(f"Error stopping process {proc.pid}: {e}")
        return False

def stop_by_port(port=9213):
    """Stop processes listening on specific port."""
    stopped = False
    
    for conn in psutil.net_connections():
        if conn.laddr.port == port and conn.status == 'LISTEN':
            try:
                proc = psutil.Process(conn.pid)
                if stop_process(proc):
                    stopped = True
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
    
    return stopped

def main():
    """Main stop function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Stop Chat Application')
    parser.add_argument('--port', type=int, default=9213, help='Port to check (default: 9213)')
    parser.add_argument('--force', action='store_true', help='Force kill processes')
    parser.add_argument('--all', action='store_true', help='Stop all related processes')
    
    args = parser.parse_args()
    
    print("Looking for chat application processes...")
    
    # Find processes by name/command
    processes = find_app_processes()
    
    if not processes:
        print("No chat application processes found by command line")
        
        # Try to find by port
        print(f"Checking port {args.port}...")
        if stop_by_port(args.port):
            print(f"Stopped process listening on port {args.port}")
            return 0
        else:
            print(f"No process found listening on port {args.port}")
            return 0
    
    # Stop found processes
    stopped_count = 0
    for proc in processes:
        timeout = 2 if args.force else 10
        if stop_process(proc, timeout):
            stopped_count += 1
    
    if stopped_count > 0:
        print(f"Stopped {stopped_count} process(es)")
        
        # Wait a moment and check if port is free
        time.sleep(2)
        port_free = True
        for conn in psutil.net_connections():
            if conn.laddr.port == args.port and conn.status == 'LISTEN':
                port_free = False
                break
        
        if port_free:
            print(f"Port {args.port} is now free")
        else:
            print(f"Warning: Port {args.port} is still in use")
    
    return 0

if __name__ == '__main__':
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\nInterrupted")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
