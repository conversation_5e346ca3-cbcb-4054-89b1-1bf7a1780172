"""
Task management module for handling asynchronous upload and processing tasks.
Provides a robust task queue system with worker pools and error handling.
"""

import time
import traceback
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
from threading import RLock
import gevent
from gevent.queue import Queue
from loguru import logger

from config import Config
from services import ServiceManager
from audio_processor import AudioProcessor
from image_processor import ImageProcessor
from sse_manager import SSESession, SSEManager
from utils import safe_json_dumps


class TaskStatus(Enum):
    """Task status enumeration."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class Task:
    """Represents a processing task."""
    task_id: str
    task_type: str
    data: Dict[str, Any]
    created_time: float = field(default_factory=time.time)
    started_time: Optional[float] = None
    completed_time: Optional[float] = None
    status: TaskStatus = TaskStatus.PENDING
    error_message: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3


class TaskManager:
    """Manages asynchronous task processing with worker pools."""
    
    def __init__(self, config: Config, service_manager: ServiceManager,
                 audio_processor: AudioProcessor, image_processor: ImageProcessor,
                 sse_manager: SSEManager):
        self.config = config
        self.service_manager = service_manager
        self.audio_processor = audio_processor
        self.image_processor = image_processor
        self.sse_manager = sse_manager
        
        # Task management
        self.task_queue = Queue()
        self.tasks: Dict[str, Task] = {}
        self.tasks_lock = RLock()
        
        # Worker management
        self.worker_count = 3  # Number of worker greenlets
        self.workers = []
        self.running = False
        
        # Statistics
        self.stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'processing_time_total': 0.0
        }
    
    def start_workers(self) -> None:
        """Start worker greenlets for processing tasks."""
        if self.running:
            logger.warning("Task workers already running")
            return
        
        self.running = True
        self.workers = []
        
        for i in range(self.worker_count):
            worker = gevent.spawn(self._worker_loop, f"worker-{i}")
            self.workers.append(worker)
        
        logger.info(f"Started {self.worker_count} task workers")
    
    def stop_workers(self) -> None:
        """Stop all worker greenlets."""
        if not self.running:
            return
        
        self.running = False
        
        # Wait for workers to finish current tasks
        gevent.joinall(self.workers, timeout=10)
        
        logger.info("All task workers stopped")
    
    def submit_upload_task(self, task_id: str, upload_data: Dict[str, Any]) -> bool:
        """
        Submit an upload processing task.
        
        Args:
            task_id: Unique task identifier
            upload_data: Upload data containing audio, image, location, etc.
            
        Returns:
            True if task was submitted successfully
        """
        try:
            task = Task(
                task_id=task_id,
                task_type="upload",
                data=upload_data
            )
            
            with self.tasks_lock:
                self.tasks[task_id] = task
                self.stats['total_tasks'] += 1
            
            self.task_queue.put(task)
            logger.info(f"Submitted upload task: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to submit upload task {task_id}: {e}")
            return False
    
    def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """Get the status of a specific task."""
        with self.tasks_lock:
            task = self.tasks.get(task_id)
            return task.status if task else None
    
    def get_task_info(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a task."""
        with self.tasks_lock:
            task = self.tasks.get(task_id)
            if not task:
                return None
            
            return {
                'task_id': task.task_id,
                'task_type': task.task_type,
                'status': task.status.value,
                'created_time': task.created_time,
                'started_time': task.started_time,
                'completed_time': task.completed_time,
                'error_message': task.error_message,
                'retry_count': task.retry_count,
                'processing_time': (
                    task.completed_time - task.started_time 
                    if task.started_time and task.completed_time 
                    else None
                )
            }
    
    def _worker_loop(self, worker_name: str) -> None:
        """Main worker loop for processing tasks."""
        logger.info(f"Task worker {worker_name} started")
        
        while self.running:
            try:
                # Get next task with timeout
                task = self.task_queue.get(timeout=1.0)
                if task is None:
                    continue
                
                self._process_task(task, worker_name)
                
            except gevent.queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Worker {worker_name} error: {e}\n{traceback.format_exc()}")
        
        logger.info(f"Task worker {worker_name} stopped")
    
    def _process_task(self, task: Task, worker_name: str) -> None:
        """
        Process a single task.
        
        Args:
            task: Task to process
            worker_name: Name of the worker processing the task
        """
        task_id = task.task_id
        logger.info(f"Worker {worker_name} processing task {task_id}")
        
        # Update task status
        with self.tasks_lock:
            task.status = TaskStatus.PROCESSING
            task.started_time = time.time()
        
        try:
            if task.task_type == "upload":
                self._process_upload_task(task)
            else:
                raise ValueError(f"Unknown task type: {task.task_type}")
            
            # Mark as completed
            with self.tasks_lock:
                task.status = TaskStatus.COMPLETED
                task.completed_time = time.time()
                self.stats['completed_tasks'] += 1
                
                if task.started_time and task.completed_time:
                    processing_time = task.completed_time - task.started_time
                    self.stats['processing_time_total'] += processing_time
            
            logger.info(f"Task {task_id} completed successfully")
            
        except Exception as e:
            error_msg = f"Task processing failed: {e}"
            logger.error(f"Task {task_id} failed: {error_msg}")
            
            with self.tasks_lock:
                task.error_message = error_msg
                task.retry_count += 1
                
                if task.retry_count <= task.max_retries:
                    # Retry the task
                    task.status = TaskStatus.PENDING
                    task.started_time = None
                    self.task_queue.put(task)
                    logger.info(f"Retrying task {task_id} (attempt {task.retry_count})")
                else:
                    # Max retries exceeded
                    task.status = TaskStatus.FAILED
                    task.completed_time = time.time()
                    self.stats['failed_tasks'] += 1
                    logger.error(f"Task {task_id} failed permanently after {task.retry_count} attempts")
    
    def _process_upload_task(self, task: Task) -> None:
        """
        Process an upload task containing audio, image, and location data.
        
        Args:
            task: Upload task to process
        """
        data = task.data
        device_id = data.get("id")
        audio_b64 = data.get("audio")
        image_b64 = data.get("video")  # Note: "video" field contains image data
        longitude = data.get("longitude")
        latitude = data.get("latitude")
        
        if not device_id:
            raise ValueError("Missing device ID in upload task")
        
        # Send initial status
        self.sse_manager.send_event_to_device(device_id, 'status', {
            'status': 'processing',
            'message': 'Processing your request...',
            'timestamp': time.time()
        })

        # Get exhibition information based on location
        exhibition_id, exhibition_name = self._get_exhibition_info(longitude, latitude)

        # Store user profile and exhibition info in Redis
        self._store_user_context(device_id, exhibition_id, exhibition_name)

        # Process audio if provided
        asr_text = None
        if audio_b64:
            self.sse_manager.send_event_to_device(device_id, 'status', {
                'status': 'processing_audio',
                'message': 'Processing audio...',
                'timestamp': time.time()
            })
            asr_text = self.audio_processor.process_base64_audio(audio_b64, device_id)
            if asr_text:
                logger.info(f"ASR result for {device_id}: '{asr_text}'")
                self.sse_manager.send_event_to_device(device_id, 'asr_result', {
                    'text': asr_text,
                    'timestamp': time.time()
                })

        # Process image if provided
        image_path = None
        if image_b64:
            self.sse_manager.send_event_to_device(device_id, 'status', {
                'status': 'processing_image',
                'message': 'Processing image...',
                'timestamp': time.time()
            })
            image_path = self.image_processor.process_base64_image(image_b64, device_id)
            if image_path:
                logger.info(f"Image saved for {device_id}: {image_path}")
                self.sse_manager.send_event_to_device(device_id, 'image_processed', {
                    'image_path': image_path,
                    'timestamp': time.time()
                })

        # Send processing results to Kafka
        self._send_processing_results(device_id, asr_text, image_path, exhibition_id)

        # Send completion status
        self.sse_manager.send_event_to_device(device_id, 'status', {
            'status': 'completed',
            'message': 'Processing completed, waiting for response...',
            'timestamp': time.time()
        })
    
    def _get_exhibition_info(self, longitude: Optional[float], 
                           latitude: Optional[float]) -> tuple[Optional[str], Optional[str]]:
        """Get exhibition information based on coordinates."""
        if longitude is None or latitude is None:
            logger.info("No coordinates provided, using default exhibition")
            return None, None
        
        try:
            from utils import validate_coordinates
            if not validate_coordinates(longitude, latitude):
                logger.warning(f"Invalid coordinates: {longitude}, {latitude}")
                return None, None
            
            return self.service_manager.external_api.get_exhibition_info(longitude, latitude)
            
        except Exception as e:
            logger.error(f"Failed to get exhibition info: {e}")
            return None, None
    
    def _store_user_context(self, device_id: str, exhibition_id: Optional[str], 
                          exhibition_name: Optional[str]) -> None:
        """Store user context information in Redis."""
        try:
            redis_client = self.service_manager.redis.client
            
            if exhibition_id is not None:
                logger.info(f"Setting exhibition info for {device_id}: {exhibition_id}, {exhibition_name}")
                exhibition_data = {
                    "id": exhibition_id,
                    "name": exhibition_name,
                    "end_id": 12,
                    "caption": ""
                }
            else:
                logger.info(f"Using default exhibition for {device_id}")
                exhibition_data = {
                    "id": 0,
                    "name": "19层",
                    "end_id": 12,
                    "caption": ""
                }
            
            redis_client.hset(device_id, mapping={
                "user_profile": safe_json_dumps({"id": "user0", "age": "adult"}),
                "exhibition": safe_json_dumps(exhibition_data),
                "mode": "free",
                "direction_id": 0
            })
            
        except Exception as e:
            logger.error(f"Failed to store user context for {device_id}: {e}")
            raise
    
    def _send_processing_results(self, device_id: str, asr_text: Optional[str], 
                               image_path: Optional[str], exhibition_id: Optional[str]) -> None:
        """Send processing results to Kafka for further processing."""
        try:
            base_message = {
                "frame_time": round(time.time(), 2),
                "longitude": 0,
                "latitude": 0,
                "degree": 0,
                "ts": 0,
                "device_id": device_id,
                "exhibition_id": exhibition_id,
                "user_id": "user0",
                "event_id": device_id,
                "context": "",
                "device_type": "phone",
                "from": "client"
            }
            
            # Send image for processing if available
            if image_path:
                # Clear previous results
                redis_client = self.service_manager.redis.client
                redis_client.hdel(device_id, "frame_rag", "image_caption")
                
                # Send for image captioning
                image_message = base_message.copy()
                image_message.update({
                    "frame": image_path,
                    "to": "image_caption",
                    "interface": "image_caption"
                })
                self.service_manager.kafka.send_message(image_message)
                logger.info(f"Sent image to image_caption: {image_path}")
            
            # Send text for RAG processing if available
            if asr_text:
                redis_client = self.service_manager.redis.client
                redis_client.hdel(device_id, "text_rag")
                
                text_message = base_message.copy()
                text_message.update({
                    "frame": None,
                    "query": asr_text,
                    "to": "text_rag",
                    "interface": "text_rag"
                })
                self.service_manager.kafka.send_message(text_message)
                logger.info(f"Sent text to RAG: {asr_text}")
            
            # Send trigger message for combined processing
            if image_path or asr_text:
                trigger_message = base_message.copy()
                trigger_message.update({
                    "frame": image_path,
                    "query": asr_text,
                    "to": "trigger",
                    "interface": "trigger"
                })
                self.service_manager.kafka.send_message(trigger_message)
                logger.info("Sent trigger message for combined processing")
            
        except Exception as e:
            logger.error(f"Failed to send processing results for {device_id}: {e}")
            raise
    
    def get_stats(self) -> Dict[str, Any]:
        """Get task processing statistics."""
        with self.tasks_lock:
            avg_processing_time = (
                self.stats['processing_time_total'] / max(self.stats['completed_tasks'], 1)
            )
            
            return {
                'total_tasks': self.stats['total_tasks'],
                'completed_tasks': self.stats['completed_tasks'],
                'failed_tasks': self.stats['failed_tasks'],
                'pending_tasks': self.task_queue.qsize(),
                'active_tasks': len([t for t in self.tasks.values() if t.status == TaskStatus.PROCESSING]),
                'average_processing_time': avg_processing_time,
                'worker_count': self.worker_count,
                'workers_running': self.running
            }
    
    def cleanup_old_tasks(self, max_age_seconds: int = 3600) -> int:
        """
        Clean up old completed/failed tasks from memory.
        
        Args:
            max_age_seconds: Maximum age for keeping tasks in memory
            
        Returns:
            Number of tasks cleaned up
        """
        current_time = time.time()
        cleaned_count = 0
        
        with self.tasks_lock:
            tasks_to_remove = []
            
            for task_id, task in self.tasks.items():
                if task.status in (TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED):
                    task_age = current_time - task.created_time
                    if task_age > max_age_seconds:
                        tasks_to_remove.append(task_id)
            
            for task_id in tasks_to_remove:
                del self.tasks[task_id]
                cleaned_count += 1
        
        if cleaned_count > 0:
            logger.info(f"Cleaned up {cleaned_count} old tasks")
        
        return cleaned_count
