import requests, base64, time

# BASE = "http://127.0.0.1:9214"

# # 1. 读取音频文件并转 base64
# with open("/private/huqq/Projects/data_place/voice_hu.wav", "rb") as f:
#     audio_b64 = "data:audio/wav;base64," + base64.b64encode(f.read()).decode()

# # 2. 提交查询
# resp = requests.post(f"{BASE}/chat/query", json={
#     "id": "user123",
#     "audio": audio_b64,
#     "longitude": 116.397,
#     "latitude": 39.908
# })
# data = resp.json()
# print("query response:", data)
# task_id = data["task_id"]

# # 3. 轮询结果
# while True:
#     r = requests.get(f"{BASE}/chat/result/{task_id}")
#     result = r.json()
#     print("poll:", result)
#     if result["status"] == "done":
#         print("全部完成，合成音频列表：", result["audio"])
#         break
#     time.sleep(2)


# url = "http://127.0.0.1:5000/stream"
# while True:
#     r = requests.get(url)
    # print(r)
    # result = r.json()
    # print("poll:", result)
    # if result["status"] == "done":
    #     print("全部完成，合成音频列表：", result["audio"])
    #     break
    # time.sleep(2)
    
    
    
import requests,os
image_path = "/private/huqq/Projects/Navigation/location_mast3r_slam/logs/img87d/"
image_path = "/private/huqq/Projects/ai_chat/test_dirs_img/"

img_list = os.listdir(image_path)
print(f"发现 {len(img_list)} 张图片")

# 预加载音频文件（假设所有请求使用同一音频）
with open('/private/huqq/Projects/data_place/voice_hu.wav', 'rb') as f:
    audio_base64 = base64.b64encode(f.read()).decode()
    
img_name = img_list[0]  # 只发送第一张图片
full_path = os.path.join(image_path, img_name)
try:
    with open(full_path, 'rb') as f:
        video_base64 = base64.b64encode(f.read()).decode()
except Exception as e:
    print(f"加载图片失败: {full_path} - {str(e)}")

url="http://0.0.0.0:9213/upload"  #测试环境
# "http://177.177.230.191:9214/chat/stream" #生产环境
# url = "http://219.141.222.248:20001/chat/stream"

resp = requests.post(url, json={
    "id":"b82d28c77341341426af1",
    "audio":"",
    "video":video_base64,
    "longitude":116.397, "latitude":39.908,}, stream=True)

for line in resp.iter_lines(decode_unicode=True):
    if not line: 
        continue
    print("chunk:", line)  # 每行一个 JSON
    #等待播放完成
    time.sleep(2)
