<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSE Chat Application Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .log {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.processing {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <h1>SSE Chat Application Test</h1>
    
    <div class="container">
        <h3>Configuration</h3>
        <label>Server URL:</label>
        <input type="text" id="serverUrl" value="http://localhost:9214">
        
        <label>Device ID:</label>
        <input type="text" id="deviceId" value="test_device_web">
    </div>
    
    <div class="container">
        <h3>SSE Stream Test</h3>
        <div id="sseStatus" class="status disconnected">Disconnected</div>
        <button id="connectBtn" onclick="connectSSE()">Connect SSE Stream</button>
        <button id="disconnectBtn" onclick="disconnectSSE()" disabled>Disconnect</button>
        <div class="log" id="sseLog"></div>
    </div>
    
    <div class="container">
        <h3>Upload Test</h3>
        <label>Test Text (simulates audio):</label>
        <input type="text" id="testText" value="Hello, this is a test message">
        
        <label>Coordinates:</label>
        <input type="text" id="longitude" value="116.3974" placeholder="Longitude">
        <input type="text" id="latitude" value="39.9093" placeholder="Latitude">
        
        <button id="uploadBtn" onclick="testUpload()">Test Upload with SSE</button>
        <div class="log" id="uploadLog"></div>
    </div>
    
    <div class="container">
        <h3>Health Check</h3>
        <button onclick="testHealth()">Check Health</button>
        <div class="log" id="healthLog"></div>
    </div>

    <script>
        let eventSource = null;
        
        function log(elementId, message) {
            const logElement = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function clearLog(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }
        
        function updateSSEStatus(status, message) {
            const statusElement = document.getElementById('sseStatus');
            statusElement.className = `status ${status}`;
            statusElement.textContent = message;
        }
        
        function connectSSE() {
            const serverUrl = document.getElementById('serverUrl').value;
            const deviceId = document.getElementById('deviceId').value;
            
            if (eventSource) {
                eventSource.close();
            }
            
            clearLog('sseLog');
            log('sseLog', 'Connecting to SSE stream...');
            
            eventSource = new EventSource(`${serverUrl}/stream/${deviceId}`);
            
            eventSource.onopen = function(event) {
                log('sseLog', 'SSE connection opened');
                updateSSEStatus('connected', 'Connected');
                document.getElementById('connectBtn').disabled = true;
                document.getElementById('disconnectBtn').disabled = false;
            };
            
            eventSource.onmessage = function(event) {
                log('sseLog', `Default event: ${event.data}`);
            };
            
            eventSource.addEventListener('connected', function(event) {
                log('sseLog', `Connected event: ${event.data}`);
            });
            
            eventSource.addEventListener('status', function(event) {
                const data = JSON.parse(event.data);
                log('sseLog', `Status: ${data.status} - ${data.message}`);
                updateSSEStatus('processing', `${data.status}: ${data.message}`);
            });
            
            eventSource.addEventListener('audio', function(event) {
                const data = JSON.parse(event.data);
                log('sseLog', `Audio ready: ${data.wav} (${data.sent})`);
            });
            
            eventSource.addEventListener('end', function(event) {
                log('sseLog', `Processing ended: ${event.data}`);
                updateSSEStatus('connected', 'Connected (processing complete)');
            });
            
            eventSource.addEventListener('error', function(event) {
                log('sseLog', `Error event: ${event.data}`);
            });
            
            eventSource.addEventListener('heartbeat', function(event) {
                log('sseLog', 'Heartbeat received');
            });
            
            eventSource.onerror = function(event) {
                log('sseLog', 'SSE connection error');
                updateSSEStatus('disconnected', 'Connection error');
                document.getElementById('connectBtn').disabled = false;
                document.getElementById('disconnectBtn').disabled = true;
            };
        }
        
        function disconnectSSE() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
                log('sseLog', 'SSE connection closed');
                updateSSEStatus('disconnected', 'Disconnected');
                document.getElementById('connectBtn').disabled = false;
                document.getElementById('disconnectBtn').disabled = true;
            }
        }
        
        function createTestAudioData() {
            // Create a minimal base64 audio data for testing
            const testText = document.getElementById('testText').value;
            // In a real app, this would be actual audio data
            return `data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT`;
        }
        
        function createTestImageData() {
            // Create a minimal base64 image data for testing
            return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
        }
        
        function testUpload() {
            const serverUrl = document.getElementById('serverUrl').value;
            const deviceId = document.getElementById('deviceId').value;
            const longitude = parseFloat(document.getElementById('longitude').value);
            const latitude = parseFloat(document.getElementById('latitude').value);
            
            clearLog('uploadLog');
            log('uploadLog', 'Starting upload test...');
            
            const uploadData = {
                id: deviceId,
                audio: createTestAudioData(),
                video: createTestImageData(), // Note: field name is "video" but contains image
                longitude: longitude,
                latitude: latitude
            };
            
            fetch(`${serverUrl}/upload`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(uploadData)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                if (response.headers.get('content-type').includes('text/event-stream')) {
                    log('uploadLog', 'Received SSE stream response');
                    return response.body.getReader();
                } else {
                    return response.json();
                }
            })
            .then(result => {
                if (result && result.getReader) {
                    // Handle SSE stream
                    const reader = result;
                    const decoder = new TextDecoder();
                    
                    function readStream() {
                        reader.read().then(({ done, value }) => {
                            if (done) {
                                log('uploadLog', 'Upload SSE stream ended');
                                return;
                            }
                            
                            const chunk = decoder.decode(value);
                            const lines = chunk.split('\n');
                            
                            for (const line of lines) {
                                if (line.trim()) {
                                    log('uploadLog', `SSE: ${line}`);
                                }
                            }
                            
                            readStream();
                        }).catch(error => {
                            log('uploadLog', `Stream error: ${error.message}`);
                        });
                    }
                    
                    readStream();
                } else {
                    // Handle JSON response
                    log('uploadLog', `Response: ${JSON.stringify(result, null, 2)}`);
                }
            })
            .catch(error => {
                log('uploadLog', `Upload error: ${error.message}`);
            });
        }
        
        function testHealth() {
            const serverUrl = document.getElementById('serverUrl').value;
            
            clearLog('healthLog');
            log('healthLog', 'Checking health...');
            
            fetch(`${serverUrl}/health`)
            .then(response => response.json())
            .then(data => {
                log('healthLog', `Health check result:\n${JSON.stringify(data, null, 2)}`);
            })
            .catch(error => {
                log('healthLog', `Health check error: ${error.message}`);
            });
        }
    </script>
</body>
</html>
