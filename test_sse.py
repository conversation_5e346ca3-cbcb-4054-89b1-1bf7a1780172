#!/usr/bin/env python3
"""
Test script for the SSE-based chat application.
Tests both upload functionality and SSE event streaming.
"""

import json
import time
import base64
import requests
from typing import Dict, Any

# Test configuration
BASE_URL = "http://localhost:9214"
DEVICE_ID = "test_device_123"

def create_test_audio_data() -> str:
    """Create a simple test audio data in base64 format."""
    # Create a minimal WAV header + some dummy data
    wav_header = b'RIFF\x24\x08\x00\x00WAVEfmt \x10\x00\x00\x00\x01\x00\x01\x00\x40\x1f\x00\x00\x80\x3e\x00\x00\x02\x00\x10\x00data\x00\x08\x00\x00'
    dummy_audio = b'\x00' * 1000  # 1KB of silence
    audio_data = wav_header + dummy_audio
    
    # Encode as base64 with data URL prefix
    b64_audio = base64.b64encode(audio_data).decode('utf-8')
    return f"data:audio/wav;base64,{b64_audio}"

def create_test_image_data() -> str:
    """Create a simple test image data in base64 format."""
    # Create a minimal 1x1 pixel PNG
    png_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'
    
    # Encode as base64 with data URL prefix
    b64_image = base64.b64encode(png_data).decode('utf-8')
    return f"data:image/png;base64,{b64_image}"

def test_upload_with_sse() -> None:
    """Test the upload endpoint that returns SSE stream."""
    print("Testing upload with SSE stream...")
    
    # Prepare test data
    upload_data = {
        "id": DEVICE_ID,
        "audio": create_test_audio_data(),
        "video": create_test_image_data(),  # Note: field name is "video" but contains image
        "longitude": 116.3974,
        "latitude": 39.9093
    }
    
    try:
        # Send upload request
        print(f"Sending upload request for device {DEVICE_ID}...")
        response = requests.post(
            f"{BASE_URL}/upload",
            json=upload_data,
            headers={'Content-Type': 'application/json'},
            stream=True,  # Enable streaming for SSE
            timeout=30
        )
        
        if response.status_code != 200:
            print(f"Upload failed with status {response.status_code}: {response.text}")
            return
        
        print("Upload successful, reading SSE stream...")
        print("-" * 50)
        
        # Read SSE events
        event_count = 0
        for line in response.iter_lines(decode_unicode=True):
            if line:
                print(f"SSE: {line}")
                event_count += 1
                
                # Stop after receiving some events or timeout
                if event_count > 20:
                    print("Received enough events, stopping...")
                    break
        
        print("-" * 50)
        print(f"Received {event_count} SSE events")
        
    except requests.exceptions.Timeout:
        print("Request timed out")
    except requests.exceptions.ConnectionError:
        print("Connection error - is the server running?")
    except Exception as e:
        print(f"Error: {e}")

def test_separate_sse_stream() -> None:
    """Test the separate SSE stream endpoint."""
    print(f"\nTesting separate SSE stream for device {DEVICE_ID}...")
    
    try:
        # Connect to SSE stream
        response = requests.get(
            f"{BASE_URL}/stream/{DEVICE_ID}",
            stream=True,
            timeout=10
        )
        
        if response.status_code != 200:
            print(f"SSE stream failed with status {response.status_code}: {response.text}")
            return
        
        print("Connected to SSE stream, waiting for events...")
        print("-" * 50)
        
        # Read SSE events
        event_count = 0
        start_time = time.time()
        
        for line in response.iter_lines(decode_unicode=True):
            if line:
                print(f"SSE: {line}")
                event_count += 1
                
                # Stop after 10 seconds or some events
                if time.time() - start_time > 10 or event_count > 10:
                    print("Timeout or enough events received, stopping...")
                    break
        
        print("-" * 50)
        print(f"Received {event_count} SSE events")
        
    except requests.exceptions.Timeout:
        print("SSE stream timed out")
    except requests.exceptions.ConnectionError:
        print("Connection error - is the server running?")
    except Exception as e:
        print(f"Error: {e}")

def test_health_check() -> None:
    """Test the health check endpoint."""
    print("\nTesting health check...")
    
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        
        if response.status_code == 200:
            health_data = response.json()
            print("Health check passed:")
            print(json.dumps(health_data, indent=2))
        else:
            print(f"Health check failed with status {response.status_code}: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("Health check failed - server not reachable")
    except Exception as e:
        print(f"Health check error: {e}")

def test_stats() -> None:
    """Test the stats endpoint."""
    print("\nTesting stats endpoint...")
    
    try:
        response = requests.get(f"{BASE_URL}/stats", timeout=5)
        
        if response.status_code == 200:
            stats_data = response.json()
            print("Stats retrieved:")
            print(json.dumps(stats_data, indent=2))
        else:
            print(f"Stats failed with status {response.status_code}: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("Stats failed - server not reachable")
    except Exception as e:
        print(f"Stats error: {e}")

def main():
    """Main test function."""
    print("SSE Chat Application Test")
    print("=" * 50)
    
    # Test basic endpoints first
    test_health_check()
    test_stats()
    
    # Test SSE functionality
    test_separate_sse_stream()
    test_upload_with_sse()
    
    print("\nTest completed!")

if __name__ == "__main__":
    main()
