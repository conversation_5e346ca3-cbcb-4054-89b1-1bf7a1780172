<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Legacy WebSocket Chat Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.connected { background-color: #d4edda; color: #155724; }
        .status.disconnected { background-color: #f8d7da; color: #721c24; }
        .status.processing { background-color: #fff3cd; color: #856404; }
        
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        
        .message {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        .message.sent { background-color: #e3f2fd; }
        .message.received { background-color: #f3e5f5; }
        .message.error { background-color: #ffebee; color: #c62828; }
        
        input, select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
        
        .form-group {
            margin: 10px 0;
        }
        
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>🧪 Legacy WebSocket Chat Test</h1>
    
    <div class="container">
        <h2>连接状态</h2>
        <div id="status" class="status disconnected">未连接</div>
        <button id="connectBtn" onclick="connect()">连接 WebSocket</button>
        <button id="disconnectBtn" onclick="disconnect()" disabled>断开连接</button>
    </div>
    
    <div class="container">
        <h2>设备配置</h2>
        <div class="form-group">
            <label>设备ID:</label>
            <input type="text" id="deviceId" value="test_device_web_123">
        </div>
        <div class="form-group">
            <label>经度:</label>
            <input type="number" id="longitude" value="116.3974" step="0.0001">
        </div>
        <div class="form-group">
            <label>纬度:</label>
            <input type="number" id="latitude" value="39.9093" step="0.0001">
        </div>
    </div>
    
    <div class="container">
        <h2>文件上传测试</h2>
        <div class="form-group">
            <label>音频文件:</label>
            <input type="file" id="audioFile" accept="audio/*">
        </div>
        <div class="form-group">
            <label>图片文件:</label>
            <input type="file" id="imageFile" accept="image/*">
        </div>
        <button onclick="uploadFiles()" id="uploadBtn" disabled>上传文件</button>
        <button onclick="uploadTestData()">上传测试数据</button>
    </div>
    
    <div class="container">
        <h2>WebSocket 消息测试</h2>
        <button onclick="sendQuery()" disabled id="queryBtn">发送查询</button>
        <button onclick="sendPlayEnd()" disabled id="playEndBtn">发送播放结束</button>
    </div>
    
    <div class="container">
        <h2>消息日志</h2>
        <button onclick="clearLog()">清空日志</button>
        <div id="log" class="log"></div>
    </div>

    <script>
        let ws = null;
        let deviceId = 'test_device_web_123';
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            logDiv.appendChild(messageDiv);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function updateStatus(status, className) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = status;
            statusDiv.className = `status ${className}`;
        }
        
        function updateButtons(connected) {
            document.getElementById('connectBtn').disabled = connected;
            document.getElementById('disconnectBtn').disabled = !connected;
            document.getElementById('uploadBtn').disabled = !connected;
            document.getElementById('queryBtn').disabled = !connected;
            document.getElementById('playEndBtn').disabled = !connected;
        }
        
        function connect() {
            deviceId = document.getElementById('deviceId').value;
            const wsUrl = `ws://localhost:9214/chat`;
            
            log(`连接到 ${wsUrl}...`);
            updateStatus('连接中...', 'processing');
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function(event) {
                log('✅ WebSocket 连接成功', 'received');
                updateStatus('已连接', 'connected');
                updateButtons(true);
                
                // 发送初始查询消息
                sendQuery();
            };
            
            ws.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    log(`📨 收到消息: ${data.msg_type || 'unknown'} - ${JSON.stringify(data)}`, 'received');
                    
                    // 自动响应音频消息
                    if (data.msg_type === 'wav') {
                        setTimeout(() => {
                            sendPlayEnd();
                        }, 1000); // 模拟播放时间
                    }
                } catch (e) {
                    log(`❌ 无效的JSON消息: ${event.data}`, 'error');
                }
            };
            
            ws.onerror = function(error) {
                log(`❌ WebSocket 错误: ${error}`, 'error');
                updateStatus('连接错误', 'disconnected');
            };
            
            ws.onclose = function(event) {
                log(`🔌 WebSocket 连接关闭: ${event.code} - ${event.reason}`, 'error');
                updateStatus('未连接', 'disconnected');
                updateButtons(false);
                ws = null;
            };
        }
        
        function disconnect() {
            if (ws) {
                ws.close();
            }
        }
        
        function sendQuery() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('❌ WebSocket 未连接', 'error');
                return;
            }
            
            const message = {
                id: deviceId,
                msg: 'query',
                data: 'test query from web client'
            };
            
            ws.send(JSON.stringify(message));
            log(`📤 发送查询: ${JSON.stringify(message)}`, 'sent');
        }
        
        function sendPlayEnd() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('❌ WebSocket 未连接', 'error');
                return;
            }
            
            const message = {
                id: deviceId,
                msg: 'play_end',
                data: 'audio playback finished'
            };
            
            ws.send(JSON.stringify(message));
            log(`📤 发送播放结束: ${JSON.stringify(message)}`, 'sent');
        }
        
        function fileToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = () => resolve(reader.result);
                reader.onerror = error => reject(error);
            });
        }
        
        async function uploadFiles() {
            const audioFile = document.getElementById('audioFile').files[0];
            const imageFile = document.getElementById('imageFile').files[0];
            
            if (!audioFile && !imageFile) {
                log('❌ 请选择至少一个文件', 'error');
                return;
            }
            
            try {
                const uploadData = {
                    id: deviceId,
                    longitude: parseFloat(document.getElementById('longitude').value),
                    latitude: parseFloat(document.getElementById('latitude').value)
                };
                
                if (audioFile) {
                    uploadData.audio = await fileToBase64(audioFile);
                }
                
                if (imageFile) {
                    uploadData.video = await fileToBase64(imageFile);
                }
                
                log('📤 上传文件中...', 'sent');
                
                const response = await fetch('/upload', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(uploadData)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    log(`✅ 文件上传成功: ${JSON.stringify(result)}`, 'received');
                } else {
                    log(`❌ 文件上传失败: ${response.status} - ${await response.text()}`, 'error');
                }
                
            } catch (error) {
                log(`❌ 上传错误: ${error}`, 'error');
            }
        }
        
        async function uploadTestData() {
            // 创建测试数据
            const testAudio = 'data:audio/wav;base64,UklGRiQIAABXQVZFZm10IBAAAAABAAEAQB8AAIA+AAACABAAZGF0YQAIAAAA';
            const testImage = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
            
            const uploadData = {
                id: deviceId,
                audio: testAudio,
                video: testImage,
                longitude: parseFloat(document.getElementById('longitude').value),
                latitude: parseFloat(document.getElementById('latitude').value)
            };
            
            try {
                log('📤 上传测试数据中...', 'sent');
                
                const response = await fetch('/upload', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(uploadData)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    log(`✅ 测试数据上传成功: ${JSON.stringify(result)}`, 'received');
                } else {
                    log(`❌ 测试数据上传失败: ${response.status} - ${await response.text()}`, 'error');
                }
                
            } catch (error) {
                log(`❌ 上传错误: ${error}`, 'error');
            }
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // 页面加载完成后自动连接
        window.onload = function() {
            log('页面加载完成，可以开始测试');
        };
    </script>
</body>
</html>
