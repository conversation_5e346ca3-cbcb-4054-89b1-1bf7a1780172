#!/usr/bin/env python3
"""
Test script for the legacy WebSocket-based chat application.
Tests both upload functionality and WebSocket communication.
"""

import json
import time
import base64
import requests
import websocket
import threading
from typing import Dict, Any

# Test configuration
BASE_URL = "http://localhost:9214"
WS_URL = "ws://localhost:9214/chat"
DEVICE_ID = "test_device_legacy_123"

def create_test_audio_data() -> str:
    """Create a simple test audio data in base64 format."""
    # Create a minimal WAV header + some dummy data
    wav_header = b'RIFF\x24\x08\x00\x00WAVEfmt \x10\x00\x00\x00\x01\x00\x01\x00\x40\x1f\x00\x00\x80\x3e\x00\x00\x02\x00\x10\x00data\x00\x08\x00\x00'
    dummy_audio = b'\x00' * 1000  # 1KB of silence
    audio_data = wav_header + dummy_audio
    
    # Encode as base64 with data URL prefix
    b64_audio = base64.b64encode(audio_data).decode('utf-8')
    return f"data:audio/wav;base64,{b64_audio}"

def create_test_image_data() -> str:
    """Create a simple test image data in base64 format."""
    # Create a minimal 1x1 pixel PNG
    png_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'
    
    # Encode as base64 with data URL prefix
    b64_image = base64.b64encode(png_data).decode('utf-8')
    return f"data:image/png;base64,{b64_image}"

class WebSocketClient:
    """WebSocket client for testing."""
    
    def __init__(self, device_id: str):
        self.device_id = device_id
        self.ws = None
        self.messages = []
        self.connected = False
        self.running = False
    
    def on_message(self, ws, message):
        """Handle incoming WebSocket message."""
        try:
            data = json.loads(message)
            self.messages.append(data)
            print(f"📨 Received: {data.get('msg_type', 'unknown')} - {data}")
            
            # Auto-respond to audio messages
            if data.get('msg_type') == 'wav':
                time.sleep(0.5)  # Simulate audio playback time
                self.send_play_end()
                
        except json.JSONDecodeError:
            print(f"❌ Invalid JSON received: {message}")
    
    def on_error(self, ws, error):
        """Handle WebSocket error."""
        print(f"❌ WebSocket error: {error}")
    
    def on_close(self, ws, close_status_code, close_msg):
        """Handle WebSocket close."""
        print(f"🔌 WebSocket closed: {close_status_code} - {close_msg}")
        self.connected = False
    
    def on_open(self, ws):
        """Handle WebSocket open."""
        print(f"✅ WebSocket connected for device {self.device_id}")
        self.connected = True
        
        # Send initial query message
        self.send_query()
    
    def connect(self):
        """Connect to WebSocket server."""
        self.ws = websocket.WebSocketApp(
            WS_URL,
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close,
            on_open=self.on_open
        )
        
        self.running = True
        # Run in a separate thread
        self.ws_thread = threading.Thread(target=self.ws.run_forever)
        self.ws_thread.daemon = True
        self.ws_thread.start()
        
        # Wait for connection
        timeout = 10
        while not self.connected and timeout > 0:
            time.sleep(0.1)
            timeout -= 0.1
        
        return self.connected
    
    def send_query(self):
        """Send query message."""
        message = {
            "id": self.device_id,
            "msg": "query",
            "data": "test query from client"
        }
        self.ws.send(json.dumps(message))
        print(f"📤 Sent query: {message}")
    
    def send_play_end(self):
        """Send play end message."""
        message = {
            "id": self.device_id,
            "msg": "play_end",
            "data": "audio playback finished"
        }
        self.ws.send(json.dumps(message))
        print(f"📤 Sent play_end: {message}")
    
    def disconnect(self):
        """Disconnect from WebSocket."""
        self.running = False
        if self.ws:
            self.ws.close()

def test_upload_with_websocket():
    """Test upload with WebSocket communication."""
    print("Testing legacy upload with WebSocket...")
    
    # Start WebSocket client
    ws_client = WebSocketClient(DEVICE_ID)
    if not ws_client.connect():
        print("❌ Failed to connect WebSocket")
        return
    
    try:
        # Prepare upload data
        upload_data = {
            "id": DEVICE_ID,
            "audio": create_test_audio_data(),
            "video": create_test_image_data(),
            "longitude": 116.3974,
            "latitude": 39.9093
        }
        
        print(f"📤 Sending upload request for device {DEVICE_ID}...")
        
        # Send upload request
        response = requests.post(
            f"{BASE_URL}/upload",
            json=upload_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Upload successful: {result}")
            
            # Wait for WebSocket messages
            print("⏳ Waiting for WebSocket messages...")
            time.sleep(10)  # Wait for processing
            
            # 长连接交互
            # ws_client.send_query()
            ws_client.send_play_end()

            # print(f"📊 Received {len(ws_client.messages)} messages:")
            # for i, msg in enumerate(ws_client.messages):
            #     print(f"  {i+1}. {msg.get('msg_type', 'unknown')}: {msg}")
                
        else:
            print(f"❌ Upload failed: {response.status_code} - {response.text}")
    
    except Exception as e:
        print(f"❌ Test error: {e}")
    
    finally:
        ws_client.disconnect()

def test_health_and_stats():
    """Test health and stats endpoints."""
    print("\n🏥 Testing health check...")
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print(f"✅ Health check passed: {response.json()}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Health check error: {e}")
    
    print("\n📊 Testing stats...")
    try:
        response = requests.get(f"{BASE_URL}/stats", timeout=5)
        if response.status_code == 200:
            print(f"✅ Stats retrieved: {response.json()}")
        else:
            print(f"❌ Stats failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Stats error: {e}")

def main():
    """Main test function."""
    print("🧪 Legacy WebSocket Chat Application Test")
    print("=" * 60)
    
    # Test basic endpoints
    test_health_and_stats()
    
    # Test WebSocket functionality
    print("\n" + "=" * 60)
    test_upload_with_websocket()
    
    print("\n" + "=" * 60)
    print("🎉 Legacy WebSocket test completed!")

if __name__ == "__main__":
    main()
